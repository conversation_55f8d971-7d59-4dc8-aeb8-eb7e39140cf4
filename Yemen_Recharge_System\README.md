# 🇾🇪 منصة تعبئة الرصيد اليمنية

منصة شاملة لتعبئة رصيد الهاتف، شراء الباقات، ودفع الفواتير في اليمن. مبنية بـ Python Flask مع تصميم احترافي وواجهة مستخدم متقدمة.

## ✨ المميزات

### 🔐 نظام المصادقة والأمان
- تسجيل دخول آمن مع JWT
- أنواع مستخدمين متعددة (عملاء، وكلاء، مشرفين)
- حماية متقدمة للبيانات الحساسة

### 📱 الخدمات الأساسية
- **شحن رصيد الهاتف**: دعم جميع الشركات اليمنية
- **شراء الباقات**: باقات الإنترنت والمكالمات
- **دفع الفواتير**: فواتير الكهرباء والماء والإنترنت
- **تحويل الأموال**: تحويلات فورية بين المستخدمين

### 🎨 واجهة المستخدم
- تصميم متجاوب يعمل على جميع الأجهزة
- دعم كامل للغة العربية والاتجاه RTL
- تأثيرات بصرية متقدمة وتجربة مستخدم سلسة
- نظام ألوان احترافي ومتسق

### 🏪 نظام الوكلاء
- لوحة تحكم خاصة للوكلاء
- إدارة العمولات والمبيعات
- تقارير مفصلة للأداء

### 👨‍💼 لوحة الإدارة
- مراقبة شاملة للنظام
- إدارة المستخدمين والوكلاء
- إحصائيات وتقارير متقدمة

## 🚀 التثبيت والتشغيل

### المتطلبات
- Python 3.8+
- pip (مدير حزم Python)

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/yemen-recharge-system.git
cd yemen-recharge-system
```

2. **إنشاء بيئة افتراضية**
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate
```

3. **تثبيت التبعيات**
```bash
pip install -r requirements.txt
```

4. **تشغيل التطبيق**
```bash
python run.py
```

5. **فتح المتصفح**
```
http://localhost:5000
```

## 🔑 الحسابات التجريبية

### مدير النظام
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`
- **الصلاحيات**: إدارة كاملة للنظام

### عميل
- **اسم المستخدم**: `customer1`
- **كلمة المرور**: `123456`
- **الرصيد**: 15,750 ريال

### وكيل
- **اسم المستخدم**: `agent1`
- **كلمة المرور**: `agent123`
- **الرصيد**: 25,000 ريال

## 📁 هيكل المشروع

```
Yemen_Recharge_System/
├── app/
│   ├── __init__.py
│   ├── main.py                 # التطبيق الرئيسي
│   └── templates/              # قوالب HTML
│       ├── base.html           # القالب الأساسي
│       ├── auth/
│       │   └── login.html      # صفحة تسجيل الدخول
│       ├── dashboard/
│       │   └── main.html       # لوحة التحكم الرئيسية
│       └── services/
│           └── recharge.html   # صفحة شحن الرصيد
├── requirements.txt            # التبعيات المطلوبة
├── run.py                     # ملف تشغيل التطبيق
└── README.md                  # هذا الملف
```

## 🎨 التقنيات المستخدمة

### الواجهة الخلفية
- **Flask**: إطار عمل Python للويب
- **Flask-Login**: إدارة جلسات المستخدمين
- **Werkzeug**: أدوات الأمان والتشفير

### الواجهة الأمامية
- **Bootstrap 5**: إطار عمل CSS متجاوب
- **Font Awesome**: مكتبة الأيقونات
- **Google Fonts (Cairo)**: خط عربي احترافي
- **Animate.css**: تأثيرات الرسوم المتحركة

### الأمان
- **تشفير كلمات المرور**: باستخدام bcrypt
- **حماية CSRF**: حماية من هجمات Cross-Site Request Forgery
- **تنظيف البيانات**: تنظيف وتحقق من جميع المدخلات

## 🔧 التخصيص والتطوير

### إضافة خدمات جديدة
1. أنشئ route جديد في `app/main.py`
2. أضف القالب المناسب في `templates/services/`
3. حدث القائمة الرئيسية في `templates/dashboard/main.html`

### تخصيص التصميم
- الألوان الأساسية في `templates/base.html` (متغيرات CSS)
- الخطوط والأحجام في نفس الملف
- إضافة تأثيرات جديدة في قسم `<style>`

### إضافة أنواع مستخدمين جديدة
1. حدث نموذج `User` في `app/main.py`
2. أضف الديكوريتر المناسب للتحقق من الصلاحيات
3. أنشئ القوالب الخاصة بالنوع الجديد

## 📊 الميزات المتقدمة

### نظام الإشعارات
- إشعارات فورية للمعاملات
- تنبيهات الأمان والتحديثات
- إشعارات البريد الإلكتروني والرسائل النصية

### التقارير والإحصائيات
- تقارير مفصلة للمعاملات
- إحصائيات الاستخدام والأداء
- تصدير البيانات بصيغ مختلفة

### API للتكامل
- واجهات برمجية للتطبيقات الخارجية
- دعم JSON و XML
- مصادقة API آمنة

## 🔒 الأمان والخصوصية

### حماية البيانات
- تشفير جميع البيانات الحساسة
- نسخ احتياطية دورية
- مراقبة الأنشطة المشبوهة

### الامتثال للمعايير
- GDPR compliance للخصوصية
- PCI DSS للمعاملات المالية
- ISO 27001 لأمان المعلومات

## 🚀 النشر والإنتاج

### متطلبات الإنتاج
- خادم Linux (Ubuntu/CentOS)
- قاعدة بيانات PostgreSQL
- خادم Redis للتخزين المؤقت
- خادم ويب Nginx
- SSL Certificate

### خطوات النشر
1. إعداد الخادم والبيئة
2. تكوين قاعدة البيانات
3. رفع الملفات وتثبيت التبعيات
4. تكوين Nginx و SSL
5. إعداد المراقبة والنسخ الاحتياطية

## 📞 الدعم والمساعدة

### التواصل
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +967-1-234567
- **الموقع**: https://yemen-recharge.com

### المساهمة في المشروع
نرحب بمساهماتكم! يرجى:
1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push للفرع
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للتفاصيل.

## 🙏 شكر وتقدير

شكر خاص لجميع المساهمين والمطورين الذين ساعدوا في إنجاز هذا المشروع.

---

**تم تطوير هذا المشروع بـ ❤️ في اليمن**
