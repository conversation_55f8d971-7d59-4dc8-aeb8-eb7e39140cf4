<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نموذج أولي - منصة تعبئة الرصيد</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: white;
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #2E7D32;
            margin-bottom: 10px;
        }

        .balance {
            background: linear-gradient(135deg, #2E7D32, #4CAF50);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(46, 125, 50, 0.3);
        }

        .balance-label {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 5px;
        }

        .balance-amount {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .balance-currency {
            font-size: 16px;
            opacity: 0.9;
        }

        .services {
            background: white;
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .services-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }

        .service-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .service-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: none;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #333;
        }

        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            background: linear-gradient(135deg, #2E7D32, #4CAF50);
            color: white;
        }

        .service-icon {
            font-size: 32px;
            margin-bottom: 10px;
            display: block;
        }

        .service-name {
            font-size: 14px;
            font-weight: 600;
        }

        .transactions {
            background: white;
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            flex: 1;
        }

        .transactions-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }

        .transaction-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .transaction-item:last-child {
            border-bottom: none;
        }

        .transaction-info {
            flex: 1;
        }

        .transaction-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .transaction-date {
            font-size: 12px;
            color: #666;
        }

        .transaction-amount {
            font-size: 16px;
            font-weight: bold;
            color: #2E7D32;
        }

        .transaction-status {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 10px;
            background: #e8f5e8;
            color: #2E7D32;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            padding: 15px 20px;
            box-shadow: 0 -5px 20px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-around;
        }

        .nav-item {
            text-align: center;
            color: #666;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .nav-item.active {
            color: #2E7D32;
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 5px;
            display: block;
        }

        .nav-label {
            font-size: 12px;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            background: white;
            margin: 50px auto;
            padding: 30px;
            border-radius: 20px;
            max-width: 350px;
            position: relative;
        }

        .modal-title {
            font-size: 20px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 20px;
            color: #333;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #2E7D32;
        }

        .btn-primary {
            width: 100%;
            background: linear-gradient(135deg, #2E7D32, #4CAF50);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
        }

        .close {
            position: absolute;
            top: 15px;
            left: 20px;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }

        @media (max-width: 480px) {
            .container {
                padding: 10px;
            }
            
            .modal-content {
                margin: 20px;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">💳 منصة تعبئة الرصيد</div>
            <div style="color: #666; font-size: 14px;">مرحباً بك، أحمد محمد</div>
        </div>

        <div class="balance">
            <div class="balance-label">رصيدك الحالي</div>
            <div class="balance-amount">15,750</div>
            <div class="balance-currency">ريال يمني</div>
        </div>

        <div class="services">
            <div class="services-title">🚀 الخدمات السريعة</div>
            <div class="service-grid">
                <a href="#" class="service-card" onclick="openModal('rechargeModal')">
                    <span class="service-icon">📞</span>
                    <div class="service-name">شحن رصيد</div>
                </a>
                <a href="#" class="service-card" onclick="openModal('packagesModal')">
                    <span class="service-icon">📦</span>
                    <div class="service-name">الباقات</div>
                </a>
                <a href="#" class="service-card" onclick="openModal('billsModal')">
                    <span class="service-icon">🧾</span>
                    <div class="service-name">الفواتير</div>
                </a>
                <a href="#" class="service-card" onclick="openModal('transferModal')">
                    <span class="service-icon">💸</span>
                    <div class="service-name">تحويل أموال</div>
                </a>
            </div>
        </div>

        <div class="transactions">
            <div class="transactions-title">📊 آخر المعاملات</div>
            
            <div class="transaction-item">
                <div class="transaction-info">
                    <div class="transaction-title">شحن رصيد - يمن موبايل</div>
                    <div class="transaction-date">اليوم، 2:30 م</div>
                </div>
                <div>
                    <div class="transaction-amount">1,000 ريال</div>
                    <div class="transaction-status">نجح</div>
                </div>
            </div>

            <div class="transaction-item">
                <div class="transaction-info">
                    <div class="transaction-title">باقة إنترنت - MTN</div>
                    <div class="transaction-date">أمس، 5:15 م</div>
                </div>
                <div>
                    <div class="transaction-amount">2,500 ريال</div>
                    <div class="transaction-status">نجح</div>
                </div>
            </div>

            <div class="transaction-item">
                <div class="transaction-info">
                    <div class="transaction-title">فاتورة كهرباء</div>
                    <div class="transaction-date">أمس، 1:20 م</div>
                </div>
                <div>
                    <div class="transaction-amount">5,000 ريال</div>
                    <div class="transaction-status" style="background: #fff3e0; color: #f57c00;">معالجة</div>
                </div>
            </div>
        </div>
    </div>

    <div class="bottom-nav">
        <a href="#" class="nav-item active">
            <span class="nav-icon">🏠</span>
            <div class="nav-label">الرئيسية</div>
        </a>
        <a href="#" class="nav-item">
            <span class="nav-icon">💳</span>
            <div class="nav-label">المحفظة</div>
        </a>
        <a href="#" class="nav-item">
            <span class="nav-icon">📊</span>
            <div class="nav-label">التقارير</div>
        </a>
        <a href="#" class="nav-item">
            <span class="nav-icon">👤</span>
            <div class="nav-label">الملف الشخصي</div>
        </a>
    </div>

    <!-- Recharge Modal -->
    <div id="rechargeModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('rechargeModal')">&times;</span>
            <div class="modal-title">📞 شحن رصيد</div>
            
            <div class="form-group">
                <label class="form-label">شركة الاتصالات</label>
                <select class="form-input">
                    <option>يمن موبايل</option>
                    <option>MTN اليمن</option>
                    <option>سبأفون</option>
                    <option>واي</option>
                </select>
            </div>

            <div class="form-group">
                <label class="form-label">رقم الهاتف</label>
                <input type="tel" class="form-input" placeholder="77X XXX XXX">
            </div>

            <div class="form-group">
                <label class="form-label">مبلغ الشحن</label>
                <select class="form-input">
                    <option>500 ريال</option>
                    <option>1,000 ريال</option>
                    <option>2,000 ريال</option>
                    <option>5,000 ريال</option>
                </select>
            </div>

            <button class="btn-primary" onclick="processRecharge()">تأكيد الشحن</button>
        </div>
    </div>

    <!-- Other Modals (simplified) -->
    <div id="packagesModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('packagesModal')">&times;</span>
            <div class="modal-title">📦 الباقات</div>
            <p style="text-align: center; color: #666; margin: 20px 0;">قريباً...</p>
        </div>
    </div>

    <div id="billsModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('billsModal')">&times;</span>
            <div class="modal-title">🧾 دفع الفواتير</div>
            <p style="text-align: center; color: #666; margin: 20px 0;">قريباً...</p>
        </div>
    </div>

    <div id="transferModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('transferModal')">&times;</span>
            <div class="modal-title">💸 تحويل أموال</div>
            <p style="text-align: center; color: #666; margin: 20px 0;">قريباً...</p>
        </div>
    </div>

    <script>
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function processRecharge() {
            alert('🎉 تم تنفيذ عملية الشحن بنجاح!\n\nهذا مجرد نموذج أولي للتوضيح.');
            closeModal('rechargeModal');
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }
    </script>
</body>
</html>
