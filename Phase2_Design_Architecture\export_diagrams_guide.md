# دليل تصدير المخططات إلى صور

## 📁 ملفات المخططات المتوفرة

تم إنشاء ملفات Mermaid منفصلة يمكن تصديرها إلى صور:

1. `database_erd.mmd` - مخطط قاعدة البيانات (ERD)
2. `system_flow.mmd` - مخطط تدفق النظام
3. `recharge_sequence.mmd` - مخطط تدفق عملية الشحن

## 🌐 الطريقة الأولى: Mermaid Live Editor (الأسهل)

### الخطوات:
1. **اذهب إلى**: https://mermaid.live/
2. **انسخ محتوى** أي ملف `.mmd` من المجلد
3. **الصق الكود** في المحرر الأيسر
4. **شاهد المخطط** في الجانب الأيمن
5. **اضغط على "Actions"** في الأعلى
6. **اختر "Download PNG"** أو **"Download SVG"**

### مميزات هذه الطريقة:
✅ سهلة وسريعة  
✅ لا تحتاج تثبيت برامج  
✅ جودة عالية للصور  
✅ تدعم PNG و SVG  

## 💻 الطريقة الثانية: Mermaid CLI (للمطورين)

### التثبيت:
```bash
npm install -g @mermaid-js/mermaid-cli
```

### الاستخدام:
```bash
# تصدير إلى PNG
mmdc -i database_erd.mmd -o database_erd.png

# تصدير إلى SVG
mmdc -i system_flow.mmd -o system_flow.svg

# تصدير مع خيارات إضافية
mmdc -i recharge_sequence.mmd -o recharge_sequence.png -w 1920 -H 1080
```

## 🔧 الطريقة الثالثة: VS Code Extension

### التثبيت:
1. افتح VS Code
2. اذهب إلى Extensions
3. ابحث عن "Mermaid Preview"
4. ثبت الإضافة

### الاستخدام:
1. افتح ملف `.mmd` في VS Code
2. اضغط `Ctrl+Shift+P` (أو `Cmd+Shift+P` على Mac)
3. اكتب "Mermaid: Preview"
4. انقر بزر الماوس الأيمن على المعاينة
5. اختر "Save as Image"

## 🌍 الطريقة الرابعة: أدوات أونلاين أخرى

### 1. Draw.io (diagrams.net)
- اذهب إلى: https://app.diagrams.net/
- اختر "Create New Diagram"
- استورد ملف Mermaid أو أعد إنشاء المخطط
- صدر كـ PNG/SVG/PDF

### 2. Lucidchart
- اذهب إلى: https://lucidchart.com/
- أنشئ حساب مجاني
- استخدم أدوات الرسم لإعادة إنشاء المخططات
- صدر بصيغ مختلفة

## 📊 إعدادات التصدير الموصى بها

### للعرض على الشاشة:
- **الصيغة**: PNG
- **العرض**: 1920px
- **الارتفاع**: 1080px
- **الجودة**: عالية

### للطباعة:
- **الصيغة**: SVG أو PDF
- **الدقة**: 300 DPI
- **الحجم**: A4 أو A3

### للويب:
- **الصيغة**: PNG أو WebP
- **العرض**: 800-1200px
- **الضغط**: متوسط للحفاظ على سرعة التحميل

## 🎨 تخصيص المخططات

### تغيير الألوان:
```mermaid
%%{init: {'theme':'base', 'themeVariables': { 'primaryColor': '#ff0000'}}}%%
```

### تغيير الخط:
```mermaid
%%{init: {'theme':'base', 'themeVariables': { 'fontFamily': 'Arial'}}}%%
```

### تغيير الحجم:
```mermaid
%%{init: {'flowchart': {'nodeSpacing': 50, 'rankSpacing': 50}}}%%
```

## 📋 قائمة مراجعة التصدير

### قبل التصدير:
- [ ] تأكد من صحة كود Mermaid
- [ ] راجع جميع النصوص والتسميات
- [ ] تحقق من وضوح العلاقات والاتصالات
- [ ] اختبر المخطط في محرر Mermaid

### بعد التصدير:
- [ ] تحقق من جودة الصورة
- [ ] تأكد من وضوح النصوص
- [ ] راجع الألوان والتنسيق
- [ ] احفظ نسخة احتياطية من الملف الأصلي

## 🔄 تحديث المخططات

عند تحديث المخططات:
1. عدل ملف `.mmd` الأصلي
2. صدر الصورة الجديدة
3. استبدل الصورة القديمة
4. حدث التوثيق إذا لزم الأمر

## 📞 المساعدة والدعم

إذا واجهت مشاكل في التصدير:
1. تأكد من صحة كود Mermaid
2. جرب محرر Mermaid Live أولاً
3. راجع وثائق Mermaid الرسمية: https://mermaid-js.github.io/mermaid/
4. ابحث عن حلول في مجتمع Mermaid على GitHub

## 📁 تنظيم الملفات

اقتراح لتنظيم الصور المصدرة:
```
Phase2_Design_Architecture/
├── diagrams/
│   ├── source/
│   │   ├── database_erd.mmd
│   │   ├── system_flow.mmd
│   │   └── recharge_sequence.mmd
│   └── images/
│       ├── database_erd.png
│       ├── system_flow.png
│       └── recharge_sequence.png
└── export_diagrams_guide.md
```

هذا الدليل يوفر جميع الطرق الممكنة لتصدير المخططات إلى صور عالية الجودة!
