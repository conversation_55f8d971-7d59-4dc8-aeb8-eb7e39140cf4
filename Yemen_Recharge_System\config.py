"""
Yemen Recharge System - Configuration
إعدادات تطبيق تعبئة الرصيد اليمني
"""

import os
from datetime import timedelta

class Config:
    """إعدادات التطبيق الأساسية"""
    
    # إعدادات Flask الأساسية
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # إعدادات قاعدة البيانات
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///yemen_recharge.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_RECORD_QUERIES = True
    
    # إعدادات Redis
    REDIS_URL = os.environ.get('REDIS_URL') or 'redis://localhost:6379/0'
    
    # إعدادات الجلسات
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    SESSION_COOKIE_SECURE = False  # True في الإنتاج مع HTTPS
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # إعدادات الأمان
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600  # ساعة واحدة
    
    # إعدادات الملفات المرفوعة
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    UPLOAD_FOLDER = 'uploads'
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'pdf'}
    
    # إعدادات البريد الإلكتروني
    MAIL_SERVER = os.environ.get('MAIL_SERVER') or 'smtp.gmail.com'
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER') or '<EMAIL>'
    
    # إعدادات الرسائل النصية
    SMS_PROVIDER = os.environ.get('SMS_PROVIDER') or 'twilio'
    TWILIO_ACCOUNT_SID = os.environ.get('TWILIO_ACCOUNT_SID')
    TWILIO_AUTH_TOKEN = os.environ.get('TWILIO_AUTH_TOKEN')
    TWILIO_PHONE_NUMBER = os.environ.get('TWILIO_PHONE_NUMBER')
    
    # إعدادات APIs الخارجية
    YEMEN_MOBILE_API_URL = os.environ.get('YEMEN_MOBILE_API_URL') or 'https://api.yemenmobile.com'
    YEMEN_MOBILE_API_KEY = os.environ.get('YEMEN_MOBILE_API_KEY')
    
    MTN_API_URL = os.environ.get('MTN_API_URL') or 'https://api.mtn.ye'
    MTN_API_KEY = os.environ.get('MTN_API_KEY')
    
    SABAPHONE_API_URL = os.environ.get('SABAPHONE_API_URL') or 'https://api.sabaphone.net.ye'
    SABAPHONE_API_KEY = os.environ.get('SABAPHONE_API_KEY')
    
    Y_API_URL = os.environ.get('Y_API_URL') or 'https://api.y.net.ye'
    Y_API_KEY = os.environ.get('Y_API_KEY')
    
    # إعدادات الدفع
    PAYMENT_GATEWAY_URL = os.environ.get('PAYMENT_GATEWAY_URL')
    PAYMENT_GATEWAY_KEY = os.environ.get('PAYMENT_GATEWAY_KEY')
    PAYMENT_GATEWAY_SECRET = os.environ.get('PAYMENT_GATEWAY_SECRET')
    
    # إعدادات التطبيق
    APP_NAME = 'منصة تعبئة الرصيد اليمنية'
    APP_VERSION = '1.0.0'
    APP_DESCRIPTION = 'منصة شاملة لتعبئة رصيد الهاتف وشراء الباقات ودفع الفواتير'
    
    # إعدادات اللغة والمنطقة الزمنية
    DEFAULT_LANGUAGE = 'ar'
    TIMEZONE = 'Asia/Aden'
    CURRENCY = 'YER'
    CURRENCY_SYMBOL = 'ريال'
    
    # إعدادات الرسوم والعمولات
    RECHARGE_FEE_PERCENTAGE = 0.01  # 1%
    RECHARGE_MIN_FEE = 10  # 10 ريال كحد أدنى
    AGENT_COMMISSION_PERCENTAGE = 0.02  # 2%
    
    # حدود المعاملات
    MIN_RECHARGE_AMOUNT = 100
    MAX_RECHARGE_AMOUNT = 50000
    MIN_TRANSFER_AMOUNT = 100
    MAX_TRANSFER_AMOUNT = 100000
    DAILY_TRANSACTION_LIMIT = 500000
    
    # إعدادات التخزين المؤقت
    CACHE_TYPE = 'redis'
    CACHE_DEFAULT_TIMEOUT = 300  # 5 دقائق
    CACHE_KEY_PREFIX = 'yemen_recharge:'
    
    # إعدادات السجلات
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FILE = 'logs/yemen_recharge.log'
    LOG_MAX_BYTES = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT = 5
    
    # إعدادات المراقبة
    SENTRY_DSN = os.environ.get('SENTRY_DSN')
    ENABLE_METRICS = os.environ.get('ENABLE_METRICS', 'false').lower() in ['true', 'on', '1']
    
    @staticmethod
    def init_app(app):
        """تهيئة التطبيق مع الإعدادات"""
        pass

class DevelopmentConfig(Config):
    """إعدادات بيئة التطوير"""
    DEBUG = True
    TESTING = False
    
    # قاعدة بيانات محلية للتطوير
    SQLALCHEMY_DATABASE_URI = os.environ.get('DEV_DATABASE_URL') or \
        'sqlite:///' + os.path.join(os.path.dirname(__file__), 'data-dev.sqlite')
    
    # تفعيل أدوات التطوير
    SQLALCHEMY_ECHO = True
    WTF_CSRF_ENABLED = False  # تعطيل CSRF في التطوير لسهولة الاختبار

class TestingConfig(Config):
    """إعدادات بيئة الاختبار"""
    TESTING = True
    DEBUG = True
    
    # قاعدة بيانات في الذاكرة للاختبارات
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    
    # تعطيل الحماية في الاختبارات
    WTF_CSRF_ENABLED = False
    LOGIN_DISABLED = True

class ProductionConfig(Config):
    """إعدادات بيئة الإنتاج"""
    DEBUG = False
    TESTING = False
    
    # إعدادات أمان الإنتاج
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    PREFERRED_URL_SCHEME = 'https'
    
    # قاعدة بيانات الإنتاج
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'postgresql://user:password@localhost/yemen_recharge'
    
    # تفعيل المراقبة في الإنتاج
    ENABLE_METRICS = True
    
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)
        
        # إعداد السجلات للإنتاج
        import logging
        from logging.handlers import RotatingFileHandler
        
        if not os.path.exists('logs'):
            os.mkdir('logs')
        
        file_handler = RotatingFileHandler(
            cls.LOG_FILE,
            maxBytes=cls.LOG_MAX_BYTES,
            backupCount=cls.LOG_BACKUP_COUNT
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        
        app.logger.setLevel(logging.INFO)
        app.logger.info('Yemen Recharge System startup')

# خريطة البيئات
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

# إعدادات شركات الاتصالات
TELECOM_PROVIDERS = {
    'يمن موبايل': {
        'name': 'Yemen Mobile',
        'code': 'YM',
        'prefixes': ['77', '71'],
        'api_url': 'https://api.yemenmobile.com',
        'supports_packages': True,
        'supports_bills': False
    },
    'MTN': {
        'name': 'MTN Yemen',
        'code': 'MTN',
        'prefixes': ['73', '78'],
        'api_url': 'https://api.mtn.ye',
        'supports_packages': True,
        'supports_bills': False
    },
    'سبأفون': {
        'name': 'SabaFon',
        'code': 'SF',
        'prefixes': ['70'],
        'api_url': 'https://api.sabaphone.net.ye',
        'supports_packages': True,
        'supports_bills': False
    },
    'واي': {
        'name': 'Y Telecom',
        'code': 'Y',
        'prefixes': ['73'],
        'api_url': 'https://api.y.net.ye',
        'supports_packages': True,
        'supports_bills': True
    }
}

# إعدادات أنواع الفواتير
BILL_TYPES = {
    'electricity': {
        'name': 'فاتورة الكهرباء',
        'provider': 'الشركة العامة للكهرباء',
        'min_amount': 1000,
        'max_amount': 100000,
        'fee_percentage': 0.005  # 0.5%
    },
    'water': {
        'name': 'فاتورة الماء',
        'provider': 'مؤسسة المياه والصرف الصحي',
        'min_amount': 500,
        'max_amount': 50000,
        'fee_percentage': 0.005
    },
    'internet': {
        'name': 'فاتورة الإنترنت',
        'provider': 'يمن نت',
        'min_amount': 2000,
        'max_amount': 20000,
        'fee_percentage': 0.01
    },
    'phone': {
        'name': 'فاتورة الهاتف الثابت',
        'provider': 'تيليمن',
        'min_amount': 1000,
        'max_amount': 30000,
        'fee_percentage': 0.005
    }
}

# إعدادات الباقات
PACKAGE_CATEGORIES = {
    'internet': 'باقات الإنترنت',
    'calls': 'باقات المكالمات',
    'sms': 'باقات الرسائل',
    'mixed': 'باقات مختلطة'
}

# إعدادات الأمان
SECURITY_SETTINGS = {
    'max_login_attempts': 5,
    'lockout_duration': 1800,  # 30 دقيقة
    'password_min_length': 6,
    'password_require_uppercase': False,
    'password_require_lowercase': False,
    'password_require_numbers': True,
    'password_require_symbols': False,
    'session_timeout': 3600,  # ساعة واحدة
    'enable_2fa': False,
    'enable_email_verification': False
}
