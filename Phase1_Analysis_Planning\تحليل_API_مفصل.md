# تحليل API مفصل - مشروع تعبئة الرصيد

## 🔌 نظرة عامة على API

بناءً على التحليل الأصلي، لدينا API شاملة تدعم خدمات متعددة لشركات الاتصالات اليمنية. سنقوم بتحليل مفصل لجميع الخدمات المتاحة.

### خصائص API الأساسية
```
- نوع الطلبات: GET requests مع parameters في URL
- التوثيق: MD5 token generation
- التشفير: MD5 hash للأمان
- الاستجابة: JSON format
- المعالجة: دعم للعمليات المتزامنة وغير المتزامنة
```

## 🏢 شركات الاتصالات المدعومة

### 1. يمن موبايل (Yemen Mobile)
```
الخدمات المتاحة:
- شحن رصيد
- باقات الإنترنت
- باقات المكالمات
- باقات الرسائل
- باقات مختلطة

أطوال أرقام الهواتف:
- 9 أرقام (الأكثر شيوعاً)
- تبدأ بـ: 77, 71, 70
```

### 2. MTN اليمن
```
الخدمات المتاحة:
- شحن رصيد
- باقات الإنترنت
- باقات المكالمات
- باقات مختلطة

أطوال أرقام الهواتف:
- 9 أرقام
- تبدأ بـ: 78, 73
```

### 3. سبأفون (Sabaphone)
```
الخدمات المتاحة:
- شحن رصيد
- باقات الإنترنت
- باقات المكالمات

أطوال أرقام الهواتف:
- 9 أرقام
- تبدأ بـ: 70
```

### 4. واي (Y)
```
الخدمات المتاحة:
- شحن رصيد
- باقات الإنترنت

أطوال أرقام الهواتف:
- 9 أرقام
- تبدأ بـ: 73
```

## 🌐 مزودو خدمات الإنترنت

### 1. يمن نت (Yemen Net)
```
الخدمات:
- دفع فواتير ADSL
- دفع فواتير الخط الثابت

أطوال أرقام الحسابات:
- متغيرة حسب نوع الخدمة
```

### 2. عدن نت (Aden Net)
```
الخدمات:
- دفع فواتير الإنترنت

خصائص خاصة:
- أرقام حسابات خاصة (مثال: 79 رقم)
```

## 🔧 تحليل Endpoints الأساسية

### 1. شحن الرصيد (Recharge)
```
Endpoint: /recharge
Method: GET
Parameters:
- UserId: معرف المستخدم في النظام
- DomainName: اسم النطاق
- UserName: اسم المستخدم
- Password: كلمة المرور
- PhoneNumber: رقم الهاتف المراد شحنه
- Amount: مبلغ الشحن
- Token: MD5 hash للتوثيق

مثال على الطلب:
GET /recharge?UserId=123&DomainName=example.com&UserName=user&Password=pass&PhoneNumber=*********&Amount=1000&Token=abc123

الاستجابة المتوقعة:
{
  "resultCode": "0",
  "resultDesc": "Success",
  "transid": "TXN123456789",
  "sequenceId": "SEQ001",
  "balance": "5000.00"
}
```

### 2. شراء الباقات (Package Purchase)
```
Endpoint: /package
Method: GET
Parameters:
- UserId: معرف المستخدم
- DomainName: اسم النطاق
- UserName: اسم المستخدم
- Password: كلمة المرور
- PhoneNumber: رقم الهاتف
- offerkey: كود الباقة
- Token: MD5 hash للتوثيق

مثال على الطلب:
GET /package?UserId=123&DomainName=example.com&UserName=user&Password=pass&PhoneNumber=*********&offerkey=PKG001&Token=abc123

الاستجابة المتوقعة:
{
  "resultCode": "0",
  "resultDesc": "Package activated successfully",
  "transid": "TXN123456790",
  "sequenceId": "SEQ002",
  "packageDetails": {
    "name": "باقة 1 جيجا",
    "validity": "30 days",
    "dataAmount": "1GB"
  }
}
```

### 3. دفع الفواتير (Bill Payment)
```
Endpoint: /billpay
Method: GET
Parameters:
- UserId: معرف المستخدم
- DomainName: اسم النطاق
- UserName: اسم المستخدم
- Password: كلمة المرور
- AccountNumber: رقم الحساب
- Amount: مبلغ الفاتورة
- ServiceProvider: مزود الخدمة
- Token: MD5 hash للتوثيق

مثال على الطلب:
GET /billpay?UserId=123&DomainName=example.com&UserName=user&Password=pass&AccountNumber=ACC123&Amount=5000&ServiceProvider=YemenNet&Token=abc123

الاستجابة المتوقعة:
{
  "resultCode": "0",
  "resultDesc": "Bill paid successfully",
  "transid": "TXN123456791",
  "sequenceId": "SEQ003",
  "receiptNumber": "RCP001234"
}
```

### 4. فحص الرصيد (Balance Inquiry)
```
Endpoint: /balance
Method: GET
Parameters:
- UserId: معرف المستخدم
- DomainName: اسم النطاق
- UserName: اسم المستخدم
- Password: كلمة المرور
- PhoneNumber: رقم الهاتف
- Token: MD5 hash للتوثيق

مثال على الطلب:
GET /balance?UserId=123&DomainName=example.com&UserName=user&Password=pass&PhoneNumber=*********&Token=abc123

الاستجابة المتوقعة:
{
  "resultCode": "0",
  "resultDesc": "Success",
  "phoneNumber": "*********",
  "balance": "2500.00",
  "currency": "YER",
  "lastRecharge": "2024-01-15 10:30:00"
}
```

### 5. حالة العملية (Operation Status)
```
Endpoint: /status
Method: GET
Parameters:
- UserId: معرف المستخدم
- DomainName: اسم النطاق
- UserName: اسم المستخدم
- Password: كلمة المرور
- transid: معرف المعاملة
- Token: MD5 hash للتوثيق

مثال على الطلب:
GET /status?UserId=123&DomainName=example.com&UserName=user&Password=pass&transid=TXN123456789&Token=abc123

الاستجابة المتوقعة:
{
  "resultCode": "0",
  "resultDesc": "Transaction completed",
  "transid": "TXN123456789",
  "status": "SUCCESS",
  "completedAt": "2024-01-15 10:35:00"
}
```

## 🔐 نظام التوثيق (Token Generation)

### خوارزمية MD5 Token
```python
import hashlib

def generate_token(user_id, domain_name, username, password, additional_params=""):
    """
    توليد MD5 token للتوثيق
    """
    # ترتيب المعاملات حسب المطلوب
    token_string = f"{user_id}{domain_name}{username}{password}{additional_params}"
    
    # إنشاء MD5 hash
    md5_hash = hashlib.md5(token_string.encode()).hexdigest()
    
    return md5_hash

# مثال على الاستخدام
token = generate_token(
    user_id="123",
    domain_name="example.com", 
    username="user",
    password="pass",
    additional_params="*********_1000"  # رقم الهاتف + المبلغ
)
```

## 📊 رموز الاستجابة (Response Codes)

### رموز النجاح
```
0: نجح العملية
1: تم قبول الطلب (قيد المعالجة)
```

### رموز الخطأ
```
-1: خطأ عام في النظام
-2: العملية قيد المعالجة (تحتاج متابعة)
-3: رصيد غير كافي
-4: رقم هاتف غير صحيح
-5: مبلغ غير صالح
-6: خطأ في التوثيق
-7: خدمة غير متاحة مؤقتاً
-8: تجاوز الحد المسموح
-9: حساب مجمد أو معطل
-10: خطأ في الشبكة
```

## 📋 جداول البيانات المطلوبة

### 1. جدول أرقام الباقات (Package Codes)
```
نحتاج للحصول على:
- offerkey لكل باقة
- اسم الباقة
- السعر
- التفاصيل (بيانات، مكالمات، رسائل)
- مدة الصلاحية
- الشركة المزودة
```

### 2. جدول أرقام الخدمات (Service Numbers)
```
نحتاج للحصول على:
- num لكل خدمة
- نوع الخدمة
- الوصف
- الشركة المزودة
- حالة التفعيل
```

### 3. جدول معرفات الحزم (Package IDs)
```
نحتاج للحصول على:
- packageid لكل حزمة
- تفاصيل الحزمة
- السعر
- الشروط والأحكام
```

## ⚠️ التحديات والاعتبارات

### 1. معالجة العمليات قيد المعالجة
```
التحدي:
- بعض العمليات تعطي resultCode = -2 (قيد المعالجة)
- تحتاج متابعة دورية لمعرفة النتيجة النهائية

الحل المقترح:
- نظام background jobs للتحقق الدوري
- استخدام OPERATION STATUS API كل 30 ثانية
- تحديث حالة المعاملة في قاعدة البيانات
- إشعار العميل عند اكتمال العملية
```

### 2. إدارة الأخطاء
```
التحدي:
- أخطاء متنوعة من API
- رسائل خطأ قد تكون غير واضحة للمستخدم

الحل المقترح:
- ترجمة رموز الخطأ لرسائل مفهومة
- نظام retry للأخطاء المؤقتة
- تسجيل مفصل للأخطاء
- نظام تنبيهات للأخطاء الحرجة
```

### 3. أمان البيانات
```
التحدي:
- حماية بيانات اعتماد API
- تأمين المعاملات المالية

الحل المقترح:
- تشفير بيانات API في قاعدة البيانات
- استخدام HTTPS لجميع الطلبات
- تدوير دوري لكلمات المرور
- مراقبة الأنشطة المشبوهة
```

## 🔄 تدفق العمليات المقترح

### 1. عملية شحن الرصيد
```
1. المستخدم يدخل رقم الهاتف والمبلغ
2. النظام يتحقق من صحة البيانات
3. النظام يتحقق من رصيد المستخدم
4. النظام يولد Token ويرسل طلب لـ API
5. النظام يتلقى الاستجابة ويحلل النتيجة
6. إذا كانت النتيجة "قيد المعالجة"، يتم جدولة متابعة
7. النظام يحدث قاعدة البيانات ويرسل إشعار للمستخدم
```

### 2. متابعة العمليات قيد المعالجة
```
1. نظام background job يعمل كل 30 ثانية
2. يستعلم عن جميع المعاملات قيد المعالجة
3. يرسل طلب OPERATION STATUS لكل معاملة
4. يحدث حالة المعاملة حسب الاستجابة
5. يرسل إشعار للمستخدم عند اكتمال العملية
```

## 📝 متطلبات التكامل الإضافية

### 1. بيانات مطلوبة من مزود API
```
الجداول الناقصة:
- جدول كامل لجميع offerkey مع تفاصيل الباقات
- جدول كامل لجميع num مع تفاصيل الخدمات
- جدول كامل لجميع packageid مع تفاصيل الحزم
- قائمة بجميع resultCode المحتملة ومعانيها
- وثائق تفصيلية لكل endpoint

بيانات الاعتماد:
- UserId الخاص بحسابنا
- DomainName المخصص لنا
- UserName و Password للوصول
- حدود المعاملات اليومية/الشهرية
- رسوم كل نوع من العمليات
```

### 2. اختبار API
```
بيئة الاختبار:
- URL خاص ببيئة التطوير/الاختبار
- بيانات اعتماد للاختبار
- أرقام هواتف وهمية للاختبار
- مبالغ محدودة للاختبار

اختبارات مطلوبة:
- اختبار جميع أنواع العمليات
- اختبار حالات الخطأ المختلفة
- اختبار العمليات قيد المعالجة
- اختبار الحدود والقيود
- اختبار الأداء تحت الضغط
```

### 3. مراقبة وصيانة API
```
مراقبة الأداء:
- مراقبة أوقات الاستجابة
- مراقبة معدلات النجاح/الفشل
- مراقبة توفر الخدمة
- تنبيهات للمشاكل الحرجة

الصيانة الدورية:
- تحديث بيانات الباقات والخدمات
- مراجعة وتحديث أكواد الخطأ
- تحديث بيانات الاعتماد عند الحاجة
- نسخ احتياطية لسجلات المعاملات
```

## 🎯 الخطوات التالية

1. **الحصول على بيانات API الكاملة** من مزود الخدمة
2. **إعداد بيئة اختبار** للتطوير والتجريب
3. **تطوير مكتبة تكامل** لـ API في النظام
4. **اختبار شامل** لجميع العمليات
5. **تطوير نظام مراقبة** للأداء والأخطاء

هذا التحليل المفصل يوفر فهماً شاملاً لـ API ومتطلبات التكامل معها.
