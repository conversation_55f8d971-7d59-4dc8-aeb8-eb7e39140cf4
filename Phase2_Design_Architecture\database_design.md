# تصميم قاعدة البيانات - مشروع تعبئة الرصيد

## 🗄️ نظرة عامة على قاعدة البيانات

### نظام إدارة قاعدة البيانات المختار
**PostgreSQL 14+**
- دعم ممتاز للمعاملات المعقدة
- أداء عالي للاستعلامات
- دعم JSON/JSONB للبيانات المرنة
- أمان متقدم وتشفير
- قابلية توسع ممتازة

### مبادئ التصميم
- **التطبيع (Normalization)**: تجنب تكرار البيانات
- **الأداء**: فهرسة محسنة للاستعلامات السريعة
- **الأمان**: تشفير البيانات الحساسة
- **المرونة**: دعم التوسع المستقبلي
- **التكامل**: ض<PERSON><PERSON> صحة البيانات

## 👥 جداول إدارة المستخدمين

### 1. جدول المستخدمين (users)
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone_number VARCHAR(15) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    user_type user_type_enum NOT NULL DEFAULT 'customer',
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    email_verified_at TIMESTAMP,
    phone_verified_at TIMESTAMP,
    last_login_at TIMESTAMP,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء enum لأنواع المستخدمين
CREATE TYPE user_type_enum AS ENUM ('customer', 'agent', 'admin', 'super_admin');
```

### 2. جدول ملفات المستخدمين (user_profiles)
```sql
CREATE TABLE user_profiles (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    date_of_birth DATE,
    gender gender_enum,
    address TEXT,
    city VARCHAR(50),
    governorate VARCHAR(50),
    national_id VARCHAR(20),
    profile_image_url VARCHAR(255),
    preferred_language language_enum DEFAULT 'arabic',
    timezone VARCHAR(50) DEFAULT 'Asia/Aden',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TYPE gender_enum AS ENUM ('male', 'female');
CREATE TYPE language_enum AS ENUM ('arabic', 'english');
```

### 3. جدول إعدادات المستخدمين (user_settings)
```sql
CREATE TABLE user_settings (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    notifications_enabled BOOLEAN DEFAULT true,
    sms_notifications BOOLEAN DEFAULT true,
    email_notifications BOOLEAN DEFAULT true,
    push_notifications BOOLEAN DEFAULT true,
    two_factor_enabled BOOLEAN DEFAULT false,
    daily_limit DECIMAL(15,2) DEFAULT 50000.00,
    monthly_limit DECIMAL(15,2) DEFAULT 1000000.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 💰 جداول إدارة المحافظ والأموال

### 4. جدول المحافظ (wallets)
```sql
CREATE TABLE wallets (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    balance DECIMAL(15,2) DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'YER',
    is_frozen BOOLEAN DEFAULT false,
    frozen_reason TEXT,
    frozen_at TIMESTAMP,
    frozen_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT positive_balance CHECK (balance >= 0)
);
```

### 5. جدول حركات المحافظ (wallet_transactions)
```sql
CREATE TABLE wallet_transactions (
    id SERIAL PRIMARY KEY,
    wallet_id INTEGER REFERENCES wallets(id),
    transaction_type wallet_transaction_type_enum NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    balance_before DECIMAL(15,2) NOT NULL,
    balance_after DECIMAL(15,2) NOT NULL,
    description TEXT,
    reference_id VARCHAR(100), -- معرف مرجعي للعملية الخارجية
    related_transaction_id INTEGER REFERENCES transactions(id),
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TYPE wallet_transaction_type_enum AS ENUM (
    'credit', 'debit', 'transfer_in', 'transfer_out', 
    'refund', 'fee', 'commission', 'bonus'
);
```

## 🏢 جداول مزودي الخدمات

### 6. جدول مزودي الخدمات (service_providers)
```sql
CREATE TABLE service_providers (
    id SERIAL PRIMARY KEY,
    name_ar VARCHAR(100) NOT NULL,
    name_en VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    provider_type provider_type_enum NOT NULL,
    logo_url VARCHAR(255),
    description_ar TEXT,
    description_en TEXT,
    is_active BOOLEAN DEFAULT true,
    api_config JSONB, -- تكوين API خاص بكل مزود
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TYPE provider_type_enum AS ENUM (
    'telecom', 'internet', 'utility', 'government', 'other'
);
```

### 7. جدول أنواع الخدمات (service_types)
```sql
CREATE TABLE service_types (
    id SERIAL PRIMARY KEY,
    provider_id INTEGER REFERENCES service_providers(id),
    name_ar VARCHAR(100) NOT NULL,
    name_en VARCHAR(100) NOT NULL,
    code VARCHAR(20) NOT NULL,
    category service_category_enum NOT NULL,
    description_ar TEXT,
    description_en TEXT,
    min_amount DECIMAL(10,2),
    max_amount DECIMAL(10,2),
    fee_percentage DECIMAL(5,4) DEFAULT 0,
    fixed_fee DECIMAL(10,2) DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TYPE service_category_enum AS ENUM (
    'recharge', 'package', 'bill_payment', 'transfer', 'other'
);
```

### 8. جدول الباقات والعروض (service_packages)
```sql
CREATE TABLE service_packages (
    id SERIAL PRIMARY KEY,
    provider_id INTEGER REFERENCES service_providers(id),
    service_type_id INTEGER REFERENCES service_types(id),
    name_ar VARCHAR(150) NOT NULL,
    name_en VARCHAR(150) NOT NULL,
    package_code VARCHAR(50) NOT NULL, -- offerkey من API
    price DECIMAL(10,2) NOT NULL,
    description_ar TEXT,
    description_en TEXT,
    validity_days INTEGER DEFAULT 30,
    data_amount VARCHAR(50), -- مثل "1GB", "5GB"
    voice_minutes INTEGER DEFAULT 0,
    sms_count INTEGER DEFAULT 0,
    package_type package_type_enum,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TYPE package_type_enum AS ENUM (
    'data', 'voice', 'sms', 'combo', 'special'
);
```

## 💳 جداول المعاملات

### 9. جدول المعاملات الرئيسي (transactions)
```sql
CREATE TABLE transactions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    transaction_id VARCHAR(50) UNIQUE NOT NULL, -- معرف فريد للعملية
    api_transaction_id VARCHAR(100), -- transid من API الخارجية
    provider_id INTEGER REFERENCES service_providers(id),
    service_type_id INTEGER REFERENCES service_types(id),
    package_id INTEGER REFERENCES service_packages(id),
    
    -- تفاصيل العملية
    target_number VARCHAR(20) NOT NULL, -- رقم الهاتف أو الحساب
    amount DECIMAL(15,2) NOT NULL,
    fee DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(15,2) NOT NULL, -- amount + fee
    
    -- حالة العملية
    status transaction_status_enum NOT NULL DEFAULT 'pending',
    api_response_code VARCHAR(10),
    api_response_desc TEXT,
    api_sequence_id VARCHAR(100),
    
    -- معلومات إضافية
    description TEXT,
    notes TEXT,
    metadata JSONB, -- بيانات إضافية مرنة
    
    -- تواريخ مهمة
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    failed_at TIMESTAMP,
    
    CONSTRAINT positive_amount CHECK (amount > 0),
    CONSTRAINT positive_total CHECK (total_amount > 0)
);

CREATE TYPE transaction_status_enum AS ENUM (
    'pending', 'processing', 'success', 'failed', 
    'cancelled', 'refunded', 'under_review'
);
```

### 10. جدول محاولات المعاملات (transaction_attempts)
```sql
CREATE TABLE transaction_attempts (
    id SERIAL PRIMARY KEY,
    transaction_id INTEGER REFERENCES transactions(id),
    attempt_number INTEGER NOT NULL,
    api_request JSONB, -- طلب API المرسل
    api_response JSONB, -- استجابة API المستلمة
    response_code VARCHAR(10),
    response_message TEXT,
    attempt_status VARCHAR(20),
    attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(transaction_id, attempt_number)
);
```

## 🏪 جداول نظام الوكلاء

### 11. جدول الوكلاء (agents)
```sql
CREATE TABLE agents (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    agent_code VARCHAR(20) UNIQUE NOT NULL,
    business_name VARCHAR(100) NOT NULL,
    business_type VARCHAR(50),
    license_number VARCHAR(50),
    tax_number VARCHAR(50),
    
    -- معلومات الموقع
    address TEXT NOT NULL,
    city VARCHAR(50) NOT NULL,
    governorate VARCHAR(50) NOT NULL,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    
    -- حالة الوكيل
    status agent_status_enum DEFAULT 'pending',
    approved_at TIMESTAMP,
    approved_by INTEGER REFERENCES users(id),
    
    -- حدود العمليات
    daily_limit DECIMAL(15,2) DEFAULT 100000.00,
    monthly_limit DECIMAL(15,2) DEFAULT 2000000.00,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TYPE agent_status_enum AS ENUM (
    'pending', 'approved', 'suspended', 'rejected', 'inactive'
);
```

### 12. جدول عمولات الوكلاء (agent_commissions)
```sql
CREATE TABLE agent_commissions (
    id SERIAL PRIMARY KEY,
    agent_id INTEGER REFERENCES agents(id),
    transaction_id INTEGER REFERENCES transactions(id),
    commission_rate DECIMAL(5,4) NOT NULL, -- نسبة العمولة
    commission_amount DECIMAL(10,2) NOT NULL,
    status commission_status_enum DEFAULT 'pending',
    calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    paid_at TIMESTAMP,
    payment_reference VARCHAR(100)
);

CREATE TYPE commission_status_enum AS ENUM (
    'pending', 'approved', 'paid', 'cancelled'
);
```

## 📊 جداول التقارير والإحصائيات

### 13. جدول الجلسات (user_sessions)
```sql
CREATE TABLE user_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    session_token VARCHAR(255) UNIQUE NOT NULL,
    ip_address INET,
    user_agent TEXT,
    device_info JSONB,
    is_active BOOLEAN DEFAULT true,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 14. جدول سجل الأنشطة (activity_logs)
```sql
CREATE TABLE activity_logs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id INTEGER,
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 15. جدول الإشعارات (notifications)
```sql
CREATE TABLE notifications (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    type notification_type_enum NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    data JSONB, -- بيانات إضافية
    is_read BOOLEAN DEFAULT false,
    read_at TIMESTAMP,
    sent_via notification_channel_enum[],
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TYPE notification_type_enum AS ENUM (
    'transaction_success', 'transaction_failed', 'low_balance',
    'security_alert', 'promotion', 'system_update', 'other'
);

CREATE TYPE notification_channel_enum AS ENUM (
    'in_app', 'sms', 'email', 'push'
);
```

## 🔐 جداول الأمان

### 16. جدول رموز التحقق (verification_codes)
```sql
CREATE TABLE verification_codes (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    code VARCHAR(10) NOT NULL,
    type verification_type_enum NOT NULL,
    purpose VARCHAR(50) NOT NULL,
    is_used BOOLEAN DEFAULT false,
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TYPE verification_type_enum AS ENUM (
    'sms', 'email', 'totp'
);
```

### 17. جدول محاولات الدخول (login_attempts)
```sql
CREATE TABLE login_attempts (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50),
    ip_address INET NOT NULL,
    user_agent TEXT,
    success BOOLEAN NOT NULL,
    failure_reason VARCHAR(100),
    attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 📈 الفهارس المحسنة للأداء

```sql
-- فهارس المستخدمين
CREATE INDEX idx_users_phone ON users(phone_number);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_type ON users(user_type);
CREATE INDEX idx_users_active ON users(is_active);

-- فهارس المعاملات
CREATE INDEX idx_transactions_user ON transactions(user_id);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_created ON transactions(created_at);
CREATE INDEX idx_transactions_provider ON transactions(provider_id);
CREATE INDEX idx_transactions_target ON transactions(target_number);

-- فهارس المحافظ
CREATE INDEX idx_wallets_user ON wallets(user_id);
CREATE INDEX idx_wallet_transactions_wallet ON wallet_transactions(wallet_id);
CREATE INDEX idx_wallet_transactions_type ON wallet_transactions(transaction_type);

-- فهارس الوكلاء
CREATE INDEX idx_agents_user ON agents(user_id);
CREATE INDEX idx_agents_status ON agents(status);
CREATE INDEX idx_agents_location ON agents(governorate, city);

-- فهارس الجلسات والأمان
CREATE INDEX idx_sessions_user ON user_sessions(user_id);
CREATE INDEX idx_sessions_token ON user_sessions(session_token);
CREATE INDEX idx_sessions_active ON user_sessions(is_active);
CREATE INDEX idx_login_attempts_ip ON login_attempts(ip_address);
```

## 🔧 جداول التكوين والإعدادات

### 18. جدول إعدادات النظام (system_settings)
```sql
CREATE TABLE system_settings (
    id SERIAL PRIMARY KEY,
    key VARCHAR(100) UNIQUE NOT NULL,
    value TEXT,
    data_type setting_data_type_enum DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT false, -- هل يمكن عرضها للمستخدمين
    updated_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TYPE setting_data_type_enum AS ENUM (
    'string', 'integer', 'decimal', 'boolean', 'json'
);
```

### 19. جدول أسعار الصرف (exchange_rates)
```sql
CREATE TABLE exchange_rates (
    id SERIAL PRIMARY KEY,
    from_currency VARCHAR(3) NOT NULL,
    to_currency VARCHAR(3) NOT NULL,
    rate DECIMAL(15,8) NOT NULL,
    effective_from TIMESTAMP NOT NULL,
    effective_to TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 20. جدول قوالب الرسائل (message_templates)
```sql
CREATE TABLE message_templates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    type template_type_enum NOT NULL,
    language language_enum DEFAULT 'arabic',
    subject VARCHAR(200), -- للإيميل
    content TEXT NOT NULL,
    variables JSONB, -- متغيرات القالب
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TYPE template_type_enum AS ENUM (
    'sms', 'email', 'push', 'in_app'
);
```

## 📋 Views للاستعلامات المعقدة

### 1. عرض إحصائيات المستخدمين
```sql
CREATE VIEW user_statistics AS
SELECT
    u.id,
    u.username,
    u.user_type,
    w.balance,
    COUNT(t.id) as total_transactions,
    SUM(CASE WHEN t.status = 'success' THEN t.amount ELSE 0 END) as total_spent,
    MAX(t.created_at) as last_transaction_date
FROM users u
LEFT JOIN wallets w ON u.id = w.user_id
LEFT JOIN transactions t ON u.id = t.user_id
GROUP BY u.id, u.username, u.user_type, w.balance;
```

### 2. عرض أداء الوكلاء
```sql
CREATE VIEW agent_performance AS
SELECT
    a.id,
    a.agent_code,
    a.business_name,
    COUNT(t.id) as total_transactions,
    SUM(t.amount) as total_volume,
    SUM(ac.commission_amount) as total_commissions,
    AVG(t.amount) as avg_transaction_amount
FROM agents a
LEFT JOIN transactions t ON a.user_id = t.user_id
LEFT JOIN agent_commissions ac ON a.id = ac.agent_id
WHERE a.status = 'approved'
GROUP BY a.id, a.agent_code, a.business_name;
```

## 🔄 Functions والـ Triggers

### 1. Function لتحديث رصيد المحفظة
```sql
CREATE OR REPLACE FUNCTION update_wallet_balance()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE wallets
        SET balance = NEW.balance_after,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = NEW.wallet_id;
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_wallet_balance
    AFTER INSERT ON wallet_transactions
    FOR EACH ROW
    EXECUTE FUNCTION update_wallet_balance();
```

### 2. Function لحساب العمولات
```sql
CREATE OR REPLACE FUNCTION calculate_commission(
    p_agent_id INTEGER,
    p_transaction_amount DECIMAL
) RETURNS DECIMAL AS $$
DECLARE
    commission_rate DECIMAL;
    commission_amount DECIMAL;
BEGIN
    -- حساب نسبة العمولة بناءً على حجم المعاملات الشهرية
    SELECT CASE
        WHEN monthly_volume < 100000 THEN 0.005  -- 0.5%
        WHEN monthly_volume < 500000 THEN 0.007  -- 0.7%
        ELSE 0.010  -- 1%
    END INTO commission_rate
    FROM (
        SELECT COALESCE(SUM(t.amount), 0) as monthly_volume
        FROM transactions t
        JOIN agents a ON a.user_id = t.user_id
        WHERE a.id = p_agent_id
        AND t.created_at >= date_trunc('month', CURRENT_DATE)
        AND t.status = 'success'
    ) monthly_stats;

    commission_amount := p_transaction_amount * commission_rate;
    RETURN commission_amount;
END;
$$ LANGUAGE plpgsql;
```

## 🔒 أمان قاعدة البيانات

### 1. Row Level Security (RLS)
```sql
-- تفعيل RLS على جدول المعاملات
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;

-- سياسة للعملاء: يمكنهم رؤية معاملاتهم فقط
CREATE POLICY customer_transactions_policy ON transactions
    FOR ALL TO customer_role
    USING (user_id = current_user_id());

-- سياسة للوكلاء: يمكنهم رؤية معاملات عملائهم
CREATE POLICY agent_transactions_policy ON transactions
    FOR ALL TO agent_role
    USING (user_id IN (
        SELECT customer_id FROM agent_customers
        WHERE agent_id = current_user_id()
    ));
```

### 2. إنشاء الأدوار والصلاحيات
```sql
-- إنشاء الأدوار
CREATE ROLE customer_role;
CREATE ROLE agent_role;
CREATE ROLE admin_role;

-- صلاحيات العملاء
GRANT SELECT, INSERT ON transactions TO customer_role;
GRANT SELECT, UPDATE ON wallets TO customer_role;
GRANT SELECT ON service_providers, service_packages TO customer_role;

-- صلاحيات الوكلاء
GRANT SELECT, INSERT ON transactions TO agent_role;
GRANT SELECT ON users, wallets TO agent_role;
GRANT ALL ON agent_commissions TO agent_role;

-- صلاحيات المشرفين
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO admin_role;
```

## 📊 إحصائيات قاعدة البيانات

### الجداول الرئيسية: 20 جدول
```
👥 إدارة المستخدمين: 3 جداول
💰 إدارة المحافظ: 2 جدول
🏢 مزودو الخدمات: 3 جداول
💳 المعاملات: 2 جدول
🏪 نظام الوكلاء: 2 جدول
📊 التقارير: 3 جداول
🔐 الأمان: 2 جدول
🔧 التكوين: 3 جداول
```

### العلاقات والقيود
```
- 25+ Foreign Key constraints
- 15+ Check constraints
- 20+ Unique constraints
- 30+ Indexes للأداء
- 5+ Views للاستعلامات المعقدة
- 3+ Functions مخصصة
- Row Level Security للأمان
```

## 🎯 الخطوات التالية

1. **مراجعة التصميم** مع فريق التطوير
2. **إنشاء Migration Scripts** لإنشاء قاعدة البيانات
3. **إضافة بيانات تجريبية** للاختبار
4. **اختبار الأداء** للاستعلامات المعقدة
5. **تطبيق إعدادات الأمان** والصلاحيات

هذا التصميم الشامل يوفر أساساً قوياً ومرناً لجميع متطلبات المشروع.
