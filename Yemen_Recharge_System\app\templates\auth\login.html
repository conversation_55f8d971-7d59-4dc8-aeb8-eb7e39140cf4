{% extends "base.html" %}

{% block title %}تسجيل الدخول - منصة تعبئة الرصيد اليمنية{% endblock %}

{% block content %}
<div class="container-fluid vh-100 d-flex align-items-center justify-content-center">
    <div class="row w-100">
        <div class="col-lg-4 col-md-6 col-sm-8 mx-auto">
            <div class="card-custom animate__animated animate__fadeInUp">
                <!-- Header -->
                <div class="text-center p-4">
                    <div class="mb-4">
                        <i class="fas fa-mobile-alt" style="font-size: 3rem; color: var(--primary-color);"></i>
                    </div>
                    <h2 class="fw-bold text-dark mb-2">منصة تعبئة الرصيد</h2>
                    <p class="text-muted">سجل دخولك للمتابعة</p>
                </div>

                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'error' if category == 'error' else category }}-custom alert-dismissible fade show mx-4" role="alert">
                                <i class="fas fa-{{ 'exclamation-circle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- Login Form -->
                <div class="p-4">
                    <form method="POST" id="loginForm">
                        <div class="mb-4">
                            <label for="username" class="form-label fw-semibold">
                                <i class="fas fa-user me-2"></i>اسم المستخدم
                            </label>
                            <input type="text" 
                                   class="form-control form-control-custom" 
                                   id="username" 
                                   name="username" 
                                   placeholder="أدخل اسم المستخدم"
                                   required>
                        </div>

                        <div class="mb-4">
                            <label for="password" class="form-label fw-semibold">
                                <i class="fas fa-lock me-2"></i>كلمة المرور
                            </label>
                            <div class="position-relative">
                                <input type="password" 
                                       class="form-control form-control-custom" 
                                       id="password" 
                                       name="password" 
                                       placeholder="أدخل كلمة المرور"
                                       required>
                                <button type="button" 
                                        class="btn btn-link position-absolute top-50 start-0 translate-middle-y border-0 text-muted"
                                        onclick="togglePassword()">
                                    <i class="fas fa-eye" id="toggleIcon"></i>
                                </button>
                            </div>
                        </div>

                        <div class="mb-4 d-flex justify-content-between align-items-center">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                <label class="form-check-label" for="remember">
                                    تذكرني
                                </label>
                            </div>
                            <a href="#" class="text-decoration-none" style="color: var(--primary-color);">
                                نسيت كلمة المرور؟
                            </a>
                        </div>

                        <button type="submit" class="btn btn-primary-custom w-100 mb-3" id="loginBtn">
                            <span id="loginText">تسجيل الدخول</span>
                            <span id="loginSpinner" class="loading d-none"></span>
                        </button>
                    </form>

                    <!-- Demo Accounts -->
                    <div class="mt-4 p-3 bg-light rounded">
                        <h6 class="fw-bold mb-3 text-center">حسابات تجريبية:</h6>
                        <div class="row g-2">
                            <div class="col-12">
                                <button class="btn btn-outline-primary-custom btn-sm w-100" onclick="fillDemo('admin', 'admin123')">
                                    <i class="fas fa-user-shield me-2"></i>مدير النظام
                                </button>
                            </div>
                            <div class="col-12">
                                <button class="btn btn-outline-primary-custom btn-sm w-100" onclick="fillDemo('customer1', '123456')">
                                    <i class="fas fa-user me-2"></i>عميل
                                </button>
                            </div>
                            <div class="col-12">
                                <button class="btn btn-outline-primary-custom btn-sm w-100" onclick="fillDemo('agent1', 'agent123')">
                                    <i class="fas fa-store me-2"></i>وكيل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <div class="text-center p-3 border-top">
                    <small class="text-muted">
                        ليس لديك حساب؟ 
                        <a href="#" class="text-decoration-none" style="color: var(--primary-color);">
                            سجل الآن
                        </a>
                    </small>
                </div>
            </div>

            <!-- Features -->
            <div class="row mt-4">
                <div class="col-4 text-center">
                    <div class="p-3">
                        <i class="fas fa-shield-alt text-success mb-2" style="font-size: 2rem;"></i>
                        <p class="small text-white fw-semibold">آمن ومحمي</p>
                    </div>
                </div>
                <div class="col-4 text-center">
                    <div class="p-3">
                        <i class="fas fa-clock text-info mb-2" style="font-size: 2rem;"></i>
                        <p class="small text-white fw-semibold">متاح 24/7</p>
                    </div>
                </div>
                <div class="col-4 text-center">
                    <div class="p-3">
                        <i class="fas fa-mobile-alt text-warning mb-2" style="font-size: 2rem;"></i>
                        <p class="small text-white fw-semibold">سهل الاستخدام</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تبديل إظهار/إخفاء كلمة المرور
    function togglePassword() {
        const passwordField = document.getElementById('password');
        const toggleIcon = document.getElementById('toggleIcon');
        
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }

    // ملء بيانات الحسابات التجريبية
    function fillDemo(username, password) {
        document.getElementById('username').value = username;
        document.getElementById('password').value = password;
        
        // تأثير بصري
        const usernameField = document.getElementById('username');
        const passwordField = document.getElementById('password');
        
        usernameField.style.background = '#e8f5e8';
        passwordField.style.background = '#e8f5e8';
        
        setTimeout(() => {
            usernameField.style.background = '';
            passwordField.style.background = '';
        }, 1000);
    }

    // معالجة إرسال النموذج
    document.getElementById('loginForm').addEventListener('submit', function(e) {
        const loginBtn = document.getElementById('loginBtn');
        const loginText = document.getElementById('loginText');
        const loginSpinner = document.getElementById('loginSpinner');
        
        // إظهار مؤشر التحميل
        loginText.classList.add('d-none');
        loginSpinner.classList.remove('d-none');
        loginBtn.disabled = true;
        
        // محاكاة تأخير الشبكة
        setTimeout(() => {
            // سيتم إرسال النموذج تلقائياً
        }, 500);
    });

    // تأثيرات الرسوم المتحركة عند التحميل
    document.addEventListener('DOMContentLoaded', function() {
        // تأثير الكتابة للعنوان
        const title = document.querySelector('h2');
        if (title) {
            title.style.opacity = '0';
            setTimeout(() => {
                title.style.transition = 'opacity 1s ease';
                title.style.opacity = '1';
            }, 300);
        }

        // تأثير الظهور للحقول
        const formFields = document.querySelectorAll('.form-control');
        formFields.forEach((field, index) => {
            field.style.opacity = '0';
            field.style.transform = 'translateX(20px)';
            
            setTimeout(() => {
                field.style.transition = 'all 0.5s ease';
                field.style.opacity = '1';
                field.style.transform = 'translateX(0)';
            }, 500 + (index * 100));
        });
    });

    // التحقق من صحة النموذج في الوقت الفعلي
    document.getElementById('username').addEventListener('input', function() {
        validateField(this);
    });

    document.getElementById('password').addEventListener('input', function() {
        validateField(this);
    });

    function validateField(field) {
        if (field.value.trim() === '') {
            field.style.borderColor = 'var(--error-color)';
        } else {
            field.style.borderColor = 'var(--success-color)';
        }
    }

    // إضافة تأثيرات الهوفر للأزرار
    document.querySelectorAll('.btn').forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
</script>
{% endblock %}
