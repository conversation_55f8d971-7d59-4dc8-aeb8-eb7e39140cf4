"""
Yemen Recharge System - Main Application
تطبيق تعبئة الرصيد اليمني - التطبيق الرئيسي
"""

from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify
from flask_login import LoginManager, login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash, generate_password_hash
from datetime import datetime, timedelta
import os
from functools import wraps
import json

# تكوين التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here-change-in-production'
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=24)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'
login_manager.login_message_category = 'info'

# نموذج المستخدم المؤقت (سيتم استبداله بقاعدة البيانات)
class User:
    def __init__(self, id, username, password_hash, full_name, phone, user_type='customer', balance=0):
        self.id = id
        self.username = username
        self.password_hash = password_hash
        self.full_name = full_name
        self.phone = phone
        self.user_type = user_type
        self.balance = balance
        self.is_active = True
        self.is_authenticated = True
        self.is_anonymous = False

    def get_id(self):
        return str(self.id)

# بيانات المستخدمين المؤقتة
users_db = {
    'admin': User(1, 'admin', generate_password_hash('admin123'), 'مدير النظام', '777123456', 'admin', 50000),
    'customer1': User(2, 'customer1', generate_password_hash('123456'), 'أحمد محمد علي', '771234567', 'customer', 15750),
    'agent1': User(3, 'agent1', generate_password_hash('agent123'), 'محل الأمل للاتصالات', '733456789', 'agent', 25000),
}

# بيانات المعاملات المؤقتة
transactions_db = [
    {
        'id': 1,
        'user_id': 2,
        'type': 'recharge',
        'provider': 'يمن موبايل',
        'target_number': '771234567',
        'amount': 1000,
        'status': 'success',
        'created_at': datetime.now() - timedelta(hours=2),
        'description': 'شحن رصيد'
    },
    {
        'id': 2,
        'user_id': 2,
        'type': 'package',
        'provider': 'MTN',
        'target_number': '733456789',
        'amount': 2500,
        'status': 'success',
        'created_at': datetime.now() - timedelta(days=1),
        'description': 'باقة إنترنت 3 جيجا'
    },
    {
        'id': 3,
        'user_id': 2,
        'type': 'bill',
        'provider': 'الكهرباء',
        'target_number': '12345678',
        'amount': 5000,
        'status': 'pending',
        'created_at': datetime.now() - timedelta(hours=5),
        'description': 'فاتورة كهرباء'
    }
]

@login_manager.user_loader
def load_user(user_id):
    for user in users_db.values():
        if user.id == int(user_id):
            return user
    return None

def admin_required(f):
    """ديكوريتر للتحقق من صلاحيات المدير"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or current_user.user_type != 'admin':
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
            return redirect(url_for('dashboard'))
        return f(*args, **kwargs)
    return decorated_function

def agent_required(f):
    """ديكوريتر للتحقق من صلاحيات الوكيل"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or current_user.user_type not in ['agent', 'admin']:
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
            return redirect(url_for('dashboard'))
        return f(*args, **kwargs)
    return decorated_function

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return render_template('index.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        remember = request.form.get('remember', False)
        
        if not username or not password:
            flash('يرجى إدخال اسم المستخدم وكلمة المرور', 'error')
            return render_template('auth/login.html')
        
        user = users_db.get(username)
        if user and check_password_hash(user.password_hash, password):
            login_user(user, remember=remember)
            next_page = request.args.get('next')
            flash(f'مرحباً بك {user.full_name}!', 'success')
            return redirect(next_page) if next_page else redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('auth/login.html')

@app.route('/logout')
@login_required
def logout():
    """تسجيل الخروج"""
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('index'))

@app.route('/dashboard')
@login_required
def dashboard():
    """لوحة التحكم الرئيسية"""
    # جلب المعاملات الخاصة بالمستخدم
    user_transactions = [t for t in transactions_db if t['user_id'] == current_user.id]
    user_transactions.sort(key=lambda x: x['created_at'], reverse=True)
    
    # إحصائيات المستخدم
    stats = {
        'total_transactions': len(user_transactions),
        'successful_transactions': len([t for t in user_transactions if t['status'] == 'success']),
        'pending_transactions': len([t for t in user_transactions if t['status'] == 'pending']),
        'total_spent': sum([t['amount'] for t in user_transactions if t['status'] == 'success'])
    }
    
    return render_template('dashboard/main.html', 
                         transactions=user_transactions[:5], 
                         stats=stats)

@app.route('/recharge')
@login_required
def recharge():
    """صفحة شحن الرصيد"""
    return render_template('services/recharge.html')

@app.route('/packages')
@login_required
def packages():
    """صفحة الباقات"""
    return render_template('services/packages.html')

@app.route('/bills')
@login_required
def bills():
    """صفحة الفواتير"""
    return render_template('services/bills.html')

@app.route('/transfer')
@login_required
def transfer():
    """صفحة التحويلات"""
    return render_template('services/transfer.html')

@app.route('/wallet')
@login_required
def wallet():
    """صفحة المحفظة"""
    user_transactions = [t for t in transactions_db if t['user_id'] == current_user.id]
    return render_template('wallet/main.html', transactions=user_transactions)

@app.route('/profile')
@login_required
def profile():
    """الملف الشخصي"""
    return render_template('profile/main.html')

@app.route('/admin')
@login_required
@admin_required
def admin_dashboard():
    """لوحة تحكم المدير"""
    total_users = len(users_db)
    total_transactions = len(transactions_db)
    total_revenue = sum([t['amount'] for t in transactions_db if t['status'] == 'success'])
    
    admin_stats = {
        'total_users': total_users,
        'total_transactions': total_transactions,
        'total_revenue': total_revenue,
        'success_rate': round((len([t for t in transactions_db if t['status'] == 'success']) / total_transactions * 100), 2) if total_transactions > 0 else 0
    }
    
    return render_template('admin/dashboard.html', 
                         stats=admin_stats, 
                         recent_transactions=transactions_db[-10:])

@app.route('/agent')
@login_required
@agent_required
def agent_dashboard():
    """لوحة تحكم الوكيل"""
    # إحصائيات الوكيل (مؤقتة)
    agent_stats = {
        'daily_sales': 15000,
        'commission_earned': 250,
        'transactions_count': 25,
        'success_rate': 98.5
    }
    
    return render_template('agent/dashboard.html', stats=agent_stats)

# API Endpoints
@app.route('/api/balance')
@login_required
def api_get_balance():
    """API للحصول على الرصيد"""
    return jsonify({'balance': current_user.balance})

@app.route('/api/process_recharge', methods=['POST'])
@login_required
def api_process_recharge():
    """API لمعالجة شحن الرصيد"""
    data = request.get_json()
    
    # محاكاة معالجة الشحن
    new_transaction = {
        'id': len(transactions_db) + 1,
        'user_id': current_user.id,
        'type': 'recharge',
        'provider': data.get('provider'),
        'target_number': data.get('phone_number'),
        'amount': int(data.get('amount')),
        'status': 'success',  # محاكاة نجاح العملية
        'created_at': datetime.now(),
        'description': 'شحن رصيد'
    }
    
    transactions_db.append(new_transaction)
    
    return jsonify({
        'success': True,
        'message': 'تم شحن الرصيد بنجاح',
        'transaction_id': new_transaction['id']
    })

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
