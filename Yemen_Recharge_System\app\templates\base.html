<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}منصة تعبئة الرصيد اليمنية{% endblock %}</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    
    <style>
        :root {
            --primary-color: #2E7D32;
            --primary-light: #4CAF50;
            --primary-dark: #1B5E20;
            --secondary-color: #1976D2;
            --success-color: #4CAF50;
            --warning-color: #FF9800;
            --error-color: #F44336;
            --info-color: #2196F3;
            --gray-50: #FAFAFA;
            --gray-100: #F5F5F5;
            --gray-200: #EEEEEE;
            --gray-300: #E0E0E0;
            --gray-500: #9E9E9E;
            --gray-700: #616161;
            --gray-900: #212121;
        }

        * {
            font-family: 'Cairo', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
            overflow: hidden;
        }

        .navbar-custom {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border: none;
            box-shadow: 0 4px 20px rgba(46, 125, 50, 0.3);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: white !important;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500;
            transition: all 0.3s ease;
            border-radius: 8px;
            margin: 0 5px;
            padding: 8px 15px !important;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white !important;
            transform: translateY(-2px);
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.3);
            color: white !important;
        }

        .card-custom {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card-custom:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .card-header-custom {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            border: none;
            padding: 20px;
            font-weight: 600;
        }

        .btn-primary-custom {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(46, 125, 50, 0.3);
        }

        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(46, 125, 50, 0.4);
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
        }

        .btn-outline-primary-custom {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            border-radius: 10px;
            padding: 10px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-outline-primary-custom:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .form-control-custom {
            border: 2px solid var(--gray-300);
            border-radius: 10px;
            padding: 15px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-control-custom:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(46, 125, 50, 0.25);
        }

        .alert-custom {
            border: none;
            border-radius: 10px;
            padding: 15px 20px;
            margin-bottom: 20px;
        }

        .alert-success-custom {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            color: var(--primary-dark);
            border-right: 4px solid var(--success-color);
        }

        .alert-error-custom {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            color: #c62828;
            border-right: 4px solid var(--error-color);
        }

        .alert-info-custom {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            color: #1565c0;
            border-right: 4px solid var(--info-color);
        }

        .stats-card {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            border: 1px solid var(--gray-200);
            transition: all 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 24px;
            color: white;
        }

        .stats-icon.primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        }

        .stats-icon.secondary {
            background: linear-gradient(135deg, var(--secondary-color), #42A5F5);
        }

        .stats-icon.success {
            background: linear-gradient(135deg, var(--success-color), #66BB6A);
        }

        .stats-icon.warning {
            background: linear-gradient(135deg, var(--warning-color), #FFB74D);
        }

        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 5px;
        }

        .stats-label {
            color: var(--gray-700);
            font-weight: 500;
        }

        .sidebar {
            background: linear-gradient(180deg, #ffffff, #f8f9fa);
            border-left: 1px solid var(--gray-200);
            min-height: calc(100vh - 120px);
            padding: 20px 0;
        }

        .sidebar-item {
            display: block;
            padding: 15px 25px;
            color: var(--gray-700);
            text-decoration: none;
            transition: all 0.3s ease;
            border-radius: 0 25px 25px 0;
            margin: 5px 0;
            margin-left: 20px;
        }

        .sidebar-item:hover {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            text-decoration: none;
            transform: translateX(-5px);
        }

        .sidebar-item.active {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
        }

        .sidebar-item i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }

        .transaction-item {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid var(--gray-200);
            transition: all 0.3s ease;
        }

        .transaction-item:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-success {
            background: #e8f5e8;
            color: var(--success-color);
        }

        .status-pending {
            background: #fff3e0;
            color: var(--warning-color);
        }

        .status-failed {
            background: #ffebee;
            color: var(--error-color);
        }

        .footer-custom {
            background: var(--gray-900);
            color: white;
            padding: 20px 0;
            margin-top: 50px;
        }

        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .sidebar {
                min-height: auto;
            }
            
            .stats-card {
                margin-bottom: 20px;
            }
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Pulse Animation */
        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    {% block content %}{% endblock %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // تأثيرات الرسوم المتحركة
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثير الظهور للعناصر
            const animatedElements = document.querySelectorAll('.card-custom, .stats-card');
            animatedElements.forEach((element, index) => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    element.style.transition = 'all 0.6s ease';
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });

        // دالة لإظهار التنبيهات
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}-custom alert-dismissible fade show`;
            alertDiv.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.main-container');
            if (container) {
                container.insertBefore(alertDiv, container.firstChild);
                
                // إزالة التنبيه تلقائياً بعد 5 ثوان
                setTimeout(() => {
                    alertDiv.remove();
                }, 5000);
            }
        }

        // دالة لتحديث الرصيد
        function updateBalance() {
            fetch('/api/balance')
                .then(response => response.json())
                .then(data => {
                    const balanceElement = document.getElementById('user-balance');
                    if (balanceElement) {
                        balanceElement.textContent = data.balance.toLocaleString('ar-YE');
                    }
                })
                .catch(error => console.error('خطأ في تحديث الرصيد:', error));
        }

        // تحديث الرصيد كل 30 ثانية
        setInterval(updateBalance, 30000);
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
