# المتطلبات الوظيفية التفصيلية - مشروع تعبئة الرصيد

## 🎯 الهدف الرئيسي
بناء منصة شاملة لإدارة الخدمات المالية والاتصالات في اليمن بنظام محاكاة داخلي متطور.

## 👥 أنواع المستخدمين

### 1. العملاء (Customers)
- **الوصف**: المستخدمون النهائيون الذين يستخدمون الخدمات
- **الصلاحيات**: شحن رصيد، شراء باقات، تحويل أموال، عرض التقارير الشخصية

### 2. الوكلاء (Agents)
- **الوصف**: وسطاء يقدمون الخدمات للعملاء مقابل عمولة
- **الصلاحيات**: إدارة عملاء، تنفيذ عمليات، عرض تقارير المبيعات، إدارة الرصيد

### 3. المشرفون (Admins)
- **الوصف**: مديرو النظام الذين يتحكمون في جميع العمليات
- **الصلاحيات**: إدارة شاملة، تقارير متقدمة، إعدادات النظام، مراقبة العمليات

## 🔧 الوظائف الأساسية

### 1. نظام إدارة المستخدمين

#### أ) التسجيل والمصادقة
```
- تسجيل حساب جديد (رقم هاتف + كلمة مرور)
- تسجيل الدخول الآمن
- استعادة كلمة المرور عبر SMS
- المصادقة الثنائية (2FA) اختيارية
- إدارة الملف الشخصي
```

#### ب) إدارة الصلاحيات
```
- تحديد نوع المستخدم (عميل/وكيل/مشرف)
- صلاحيات مخصصة لكل نوع
- إمكانية ترقية/تخفيض الصلاحيات
- تجميد/إلغاء تجميد الحسابات
```

### 2. نظام شحن الرصيد

#### أ) شحن رصيد الهاتف
```
المدخلات المطلوبة:
- رقم الهاتف (مع التحقق من صحة الرقم)
- مزود الخدمة (يمن موبايل، MTN، سبأفون، واي)
- مبلغ الشحن (مع حدود دنيا وعليا)
- طريقة الدفع (محفظة، نقداً لدى وكيل)

العمليات:
- التحقق من صحة البيانات
- التحقق من توفر الرصيد
- تنفيذ عملية الشحن (محاكاة)
- إرسال إشعار بالنتيجة
- تحديث سجل المعاملات
```

#### ب) شحن رصيد المحفظة
```
- إيداع نقدي لدى وكيل
- تحويل من مستخدم آخر
- شحن من بطاقة ائتمان (مستقبلياً)
- تحويل بنكي (مستقبلياً)
```

### 3. نظام الباقات والعروض

#### أ) إدارة الباقات
```
أنواع الباقات:
- باقات الإنترنت (1GB, 2GB, 5GB, 10GB, إلخ)
- باقات المكالمات (100 دقيقة, 200 دقيقة, إلخ)
- باقات الرسائل (100 SMS, 500 SMS, إلخ)
- باقات مختلطة (إنترنت + مكالمات + رسائل)

خصائص كل باقة:
- السعر
- مدة الصلاحية
- الوصف التفصيلي
- الشركة المزودة
- حالة التفعيل
```

#### ب) شراء الباقات
```
العملية:
- اختيار الباقة المناسبة
- التحقق من التوافق مع الشبكة
- التحقق من توفر الرصيد
- تنفيذ عملية الشراء
- تفعيل الباقة (محاكاة)
- إرسال تأكيد للعميل
```

### 4. نظام دفع الفواتير

#### أ) أنواع الفواتير المدعومة
```
- فواتير الكهرباء
- فواتير المياه
- فواتير الإنترنت (يمن نت، عدن نت)
- فواتير الهاتف الثابت
- فواتير خدمات حكومية
```

#### ب) عملية دفع الفاتورة
```
المدخلات:
- نوع الفاتورة
- رقم الحساب/العداد
- مبلغ الفاتورة (أو استعلام تلقائي)

العملية:
- التحقق من صحة البيانات
- استعلام عن قيمة الفاتورة (محاكاة)
- تأكيد الدفع من المستخدم
- تنفيذ عملية الدفع
- إصدار إيصال الدفع
```

### 5. نظام التحويلات المالية

#### أ) التحويل بين المستخدمين
```
المدخلات:
- رقم هاتف المستقبل أو معرف المستخدم
- مبلغ التحويل
- ملاحظة (اختيارية)
- رمز التأكيد (PIN)

العملية:
- التحقق من هوية المستقبل
- التحقق من توفر الرصيد
- حساب الرسوم
- تنفيذ التحويل
- إشعار الطرفين
```

#### ب) السحب النقدي
```
- طلب سحب نقدي من وكيل
- تحديد المبلغ والوكيل
- إنشاء رمز سحب مؤقت
- تأكيد السحب من الوكيل
- خصم المبلغ من المحفظة
```

### 6. نظام الوكلاء المتقدم

#### أ) إدارة الوكلاء
```
تسجيل وكيل جديد:
- البيانات الشخصية والتجارية
- الموقع الجغرافي
- رأس المال المطلوب
- الوثائق المطلوبة
- الموافقة من الإدارة

إدارة رصيد الوكيل:
- رصيد أساسي للعمليات
- حدود يومية/شهرية
- تقارير الاستخدام
- تجديد الرصيد
```

#### ب) عمليات الوكيل
```
خدمات يقدمها الوكيل:
- شحن رصيد للعملاء
- إيداع نقدي في المحافظ
- سحب نقدي من المحافظ
- بيع الباقات
- دفع الفواتير

نظام العمولات:
- عمولة ثابتة أو نسبة مئوية
- عمولات متدرجة حسب الحجم
- مكافآت شهرية للأداء المتميز
- تقارير العمولات التفصيلية
```

### 7. نظام التقارير والإحصائيات

#### أ) تقارير العملاء
```
- سجل المعاملات الشخصية
- إحصائيات الاستخدام الشهرية
- تقرير الرصيد والحركات
- تاريخ الباقات المشتراة
```

#### ب) تقارير الوكلاء
```
- تقارير المبيعات اليومية/الشهرية
- إحصائيات العملاء
- تقارير العمولات
- أداء الخدمات المختلفة
```

#### ج) تقارير الإدارة
```
- إحصائيات شاملة للنظام
- تقارير مالية مفصلة
- تحليل أداء الوكلاء
- إحصائيات استخدام الخدمات
- تقارير الأمان والمراقبة
```

### 8. نظام الإشعارات

#### أ) أنواع الإشعارات
```
- إشعارات SMS للعمليات المهمة
- إشعارات داخل التطبيق
- إشعارات البريد الإلكتروني
- إشعارات Push للتطبيق المحمول
```

#### ب) محتوى الإشعارات
```
- تأكيد العمليات الناجحة
- تنبيهات الأخطاء أو الفشل
- تذكيرات الفواتير المستحقة
- عروض وخصومات خاصة
- تحديثات النظام المهمة
```

## 🔒 المتطلبات الأمنية

### 1. أمان البيانات
```
- تشفير كلمات المرور (bcrypt)
- تشفير البيانات الحساسة في قاعدة البيانات
- استخدام HTTPS لجميع الاتصالات
- حماية من هجمات SQL Injection
- حماية من هجمات XSS و CSRF
```

### 2. أمان المعاملات
```
- رمز PIN للعمليات المالية
- حدود يومية للمعاملات
- مراقبة الأنشطة المشبوهة
- تجميد تلقائي للحسابات المشبوهة
- سجل مراجعة شامل لجميع العمليات
```

### 3. أمان الوصول
```
- جلسات محدودة الوقت
- تسجيل خروج تلقائي
- مراقبة محاولات الدخول الفاشلة
- حظر IP للمحاولات المشبوهة
- المصادقة الثنائية للحسابات الحساسة
```

## 📱 متطلبات واجهة المستخدم

### 1. سهولة الاستخدام
```
- واجهة بديهية وبسيطة
- دعم اللغة العربية بالكامل
- تصميم متجاوب لجميع الأجهزة
- أيقونات واضحة ومفهومة
- رسائل خطأ واضحة ومفيدة
```

### 2. إمكانية الوصول
```
- دعم قارئات الشاشة
- تباين ألوان مناسب
- أحجام خطوط قابلة للتعديل
- دعم التنقل بلوحة المفاتيح
- وضع الألوان العالية التباين
```

هذه هي المتطلبات الوظيفية الأساسية. هل تريد التوسع في أي جانب معين أو الانتقال للمتطلبات غير الوظيفية؟
