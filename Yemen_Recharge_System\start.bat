@echo off
chcp 65001 > nul
echo.
echo ========================================
echo    🇾🇪 منصة تعبئة الرصيد اليمنية
echo ========================================
echo.
echo 🚀 بدء تشغيل التطبيق...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت. يرجى تثبيت Python 3.8+ أولاً
    echo 📥 تحميل من: https://python.org/downloads
    pause
    exit /b 1
)

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متوفر
    pause
    exit /b 1
)

REM إنشاء البيئة الافتراضية إذا لم تكن موجودة
if not exist "venv" (
    echo 📦 إنشاء البيئة الافتراضية...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ فشل في إنشاء البيئة الافتراضية
        pause
        exit /b 1
    )
)

REM تفعيل البيئة الافتراضية
echo 🔧 تفعيل البيئة الافتراضية...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ❌ فشل في تفعيل البيئة الافتراضية
    pause
    exit /b 1
)

REM تثبيت التبعيات
echo 📚 تثبيت التبعيات المطلوبة...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ فشل في تثبيت التبعيات
    pause
    exit /b 1
)

echo.
echo ✅ تم إعداد التطبيق بنجاح!
echo.
echo 🔐 الحسابات التجريبية:
echo    👨‍💼 مدير: admin / admin123
echo    👤 عميل: customer1 / 123456  
echo    🏪 وكيل: agent1 / agent123
echo.
echo 🌐 سيتم فتح التطبيق على: http://localhost:5000
echo.
echo 🚀 بدء التشغيل...
echo.

REM تشغيل التطبيق
python run.py

pause
