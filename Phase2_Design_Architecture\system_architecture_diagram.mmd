graph TB
    %% طبقة المستخدمين
    subgraph "Users Layer"
        Customer[👤 العملاء<br/>Customers]
        Agent[🏪 الوكلاء<br/>Agents]
        Admin[👨‍💼 المشرفون<br/>Admins]
    end
    
    %% طبقة العرض
    subgraph "Presentation Layer"
        WebApp[🌐 تطبيق الويب<br/>Next.js + React]
        MobileApp[📱 التطبيق المحمول<br/>React Native]
        AdminPanel[⚙️ لوحة الإدارة<br/>Django Admin]
    end
    
    %% Load Balancer & CDN
    subgraph "Infrastructure"
        CDN[🌍 CDN<br/>CloudFront]
        LB[⚖️ Load Balancer<br/>AWS ALB]
        WAF[🛡️ Web Application Firewall]
    end
    
    %% API Gateway
    subgraph "API Gateway Layer"
        APIGateway[🚪 API Gateway<br/>Django + DRF]
        Auth[🔐 Authentication<br/>JWT + 2FA]
        RateLimit[⏱️ Rate Limiting<br/>Redis]
    end
    
    %% Business Logic Layer
    subgraph "Business Logic Layer"
        AuthService[🔑 Auth Service]
        TransactionService[💳 Transaction Service]
        WalletService[💰 Wallet Service]
        AgentService[🏪 Agent Service]
        NotificationService[📢 Notification Service]
        ReportService[📊 Report Service]
    end
    
    %% Background Jobs
    subgraph "Background Processing"
        Celery[🔄 Celery Workers]
        Scheduler[⏰ Celery Beat]
        Queue[📋 Redis Queue]
    end
    
    %% Data Access Layer
    subgraph "Data Access Layer"
        Repository[📚 Repository Pattern]
        ORM[🗃️ Django ORM]
        Cache[⚡ Redis Cache]
    end
    
    %% Data Layer
    subgraph "Data Layer"
        PostgreSQL[(🗄️ PostgreSQL<br/>Primary Database)]
        Redis[(🔴 Redis<br/>Cache & Sessions)]
        S3[(📦 AWS S3<br/>File Storage)]
    end
    
    %% External Services
    subgraph "External APIs"
        YemenMobileAPI[📞 Yemen Mobile API]
        MTNAPI[📞 MTN API]
        SabaphoneAPI[📞 Sabaphone API]
        YemenNetAPI[🌐 Yemen Net API]
    end
    
    %% Monitoring & Logging
    subgraph "Monitoring"
        Sentry[🐛 Sentry<br/>Error Tracking]
        Prometheus[📊 Prometheus<br/>Metrics]
        Grafana[📈 Grafana<br/>Dashboards]
        ELK[📋 ELK Stack<br/>Logging]
    end
    
    %% Connections
    Customer --> WebApp
    Customer --> MobileApp
    Agent --> WebApp
    Agent --> MobileApp
    Admin --> AdminPanel
    
    WebApp --> CDN
    MobileApp --> CDN
    AdminPanel --> LB
    
    CDN --> WAF
    WAF --> LB
    LB --> APIGateway
    
    APIGateway --> Auth
    APIGateway --> RateLimit
    Auth --> AuthService
    
    APIGateway --> TransactionService
    APIGateway --> WalletService
    APIGateway --> AgentService
    APIGateway --> NotificationService
    APIGateway --> ReportService
    
    TransactionService --> Queue
    NotificationService --> Queue
    Queue --> Celery
    Scheduler --> Celery
    
    AuthService --> Repository
    TransactionService --> Repository
    WalletService --> Repository
    AgentService --> Repository
    NotificationService --> Repository
    ReportService --> Repository
    
    Repository --> ORM
    Repository --> Cache
    ORM --> PostgreSQL
    Cache --> Redis
    
    TransactionService --> YemenMobileAPI
    TransactionService --> MTNAPI
    TransactionService --> SabaphoneAPI
    TransactionService --> YemenNetAPI
    
    %% Monitoring connections
    APIGateway -.-> Sentry
    TransactionService -.-> Prometheus
    PostgreSQL -.-> ELK
    Redis -.-> ELK
    
    %% Styling
    classDef userClass fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef presentationClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef infrastructureClass fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef apiClass fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef businessClass fill:#fff8e1,stroke:#f57f17,stroke-width:2px
    classDef dataClass fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef externalClass fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef monitoringClass fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    
    class Customer,Agent,Admin userClass
    class WebApp,MobileApp,AdminPanel presentationClass
    class CDN,LB,WAF infrastructureClass
    class APIGateway,Auth,RateLimit apiClass
    class AuthService,TransactionService,WalletService,AgentService,NotificationService,ReportService,Celery,Scheduler,Queue,Repository,ORM,Cache businessClass
    class PostgreSQL,Redis,S3 dataClass
    class YemenMobileAPI,MTNAPI,SabaphoneAPI,YemenNetAPI externalClass
    class Sentry,Prometheus,Grafana,ELK monitoringClass
