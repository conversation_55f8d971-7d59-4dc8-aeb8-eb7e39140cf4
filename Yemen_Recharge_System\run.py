#!/usr/bin/env python3
"""
Yemen Recharge System - Application Runner
تطبيق تعبئة الرصيد اليمني - ملف التشغيل الرئيسي
"""

import os
import sys

# إضافة مجلد التطبيق إلى مسار Python
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from app.main import app
except ImportError as e:
    print(f"خطأ في استيراد التطبيق: {e}")
    print("تأكد من وجود مجلد app وملف main.py")
    sys.exit(1)

# إضافة مجلد التطبيق إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# إعداد متغيرات البيئة للتطوير
if not os.getenv('FLASK_ENV'):
    os.environ['FLASK_ENV'] = 'development'

if not os.getenv('FLASK_DEBUG'):
    os.environ['FLASK_DEBUG'] = '1'

# إضافة فلاتر Jinja2 مخصصة
@app.template_filter('number_format')
def number_format(value):
    """تنسيق الأرقام بالفواصل"""
    try:
        return f"{int(value):,}".replace(',', '،')
    except (ValueError, TypeError):
        return value

@app.template_filter('currency')
def currency_format(value):
    """تنسيق العملة"""
    try:
        return f"{int(value):,} ريال".replace(',', '،')
    except (ValueError, TypeError):
        return f"{value} ريال"

# إضافة دوال مساعدة للقوالب
@app.template_global()
def moment():
    """دالة للتعامل مع التواريخ"""
    from datetime import datetime
    return datetime.now()

# معالج الأخطاء 404
@app.errorhandler(404)
def not_found_error(error):
    return '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>الصفحة غير موجودة - منصة تعبئة الرصيد</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
        <style>
            body { 
                font-family: 'Cairo', sans-serif; 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .error-container {
                background: white;
                border-radius: 20px;
                padding: 50px;
                text-align: center;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                max-width: 500px;
            }
            .error-icon {
                font-size: 5rem;
                color: #6c757d;
                margin-bottom: 20px;
            }
            .btn-primary-custom {
                background: linear-gradient(135deg, #2E7D32, #4CAF50);
                border: none;
                border-radius: 10px;
                padding: 12px 30px;
                font-weight: 600;
                color: white;
                text-decoration: none;
                display: inline-block;
                transition: all 0.3s ease;
            }
            .btn-primary-custom:hover {
                transform: translateY(-2px);
                color: white;
                text-decoration: none;
            }
        </style>
    </head>
    <body>
        <div class="error-container">
            <i class="fas fa-exclamation-triangle error-icon"></i>
            <h2 class="fw-bold mb-3">الصفحة غير موجودة</h2>
            <p class="text-muted mb-4">عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها.</p>
            <a href="/" class="btn-primary-custom">
                <i class="fas fa-home me-2"></i>العودة للرئيسية
            </a>
        </div>
    </body>
    </html>
    ''', 404

# معالج الأخطاء 500
@app.errorhandler(500)
def internal_error(error):
    return '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>خطأ في الخادم - منصة تعبئة الرصيد</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
        <style>
            body { 
                font-family: 'Cairo', sans-serif; 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .error-container {
                background: white;
                border-radius: 20px;
                padding: 50px;
                text-align: center;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                max-width: 500px;
            }
            .error-icon {
                font-size: 5rem;
                color: #dc3545;
                margin-bottom: 20px;
            }
            .btn-primary-custom {
                background: linear-gradient(135deg, #2E7D32, #4CAF50);
                border: none;
                border-radius: 10px;
                padding: 12px 30px;
                font-weight: 600;
                color: white;
                text-decoration: none;
                display: inline-block;
                transition: all 0.3s ease;
            }
            .btn-primary-custom:hover {
                transform: translateY(-2px);
                color: white;
                text-decoration: none;
            }
        </style>
    </head>
    <body>
        <div class="error-container">
            <i class="fas fa-exclamation-circle error-icon"></i>
            <h2 class="fw-bold mb-3">خطأ في الخادم</h2>
            <p class="text-muted mb-4">عذراً، حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى لاحقاً.</p>
            <a href="/" class="btn-primary-custom">
                <i class="fas fa-home me-2"></i>العودة للرئيسية
            </a>
        </div>
    </body>
    </html>
    ''', 500

# إضافة صفحة رئيسية بسيطة للزوار غير المسجلين
@app.route('/index')
def index_page():
    return '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>منصة تعبئة الرصيد اليمنية</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
        <style>
            body { 
                font-family: 'Cairo', sans-serif; 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
            }
            .hero-section {
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                text-align: center;
                color: white;
            }
            .hero-content {
                max-width: 600px;
                padding: 50px;
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                border-radius: 20px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
            .hero-icon {
                font-size: 5rem;
                margin-bottom: 30px;
                color: #4CAF50;
            }
            .btn-primary-custom {
                background: linear-gradient(135deg, #2E7D32, #4CAF50);
                border: none;
                border-radius: 10px;
                padding: 15px 40px;
                font-weight: 600;
                color: white;
                text-decoration: none;
                display: inline-block;
                transition: all 0.3s ease;
                margin: 10px;
            }
            .btn-primary-custom:hover {
                transform: translateY(-2px);
                color: white;
                text-decoration: none;
                box-shadow: 0 10px 30px rgba(46, 125, 50, 0.3);
            }
            .feature-card {
                background: rgba(255, 255, 255, 0.9);
                border-radius: 15px;
                padding: 30px;
                margin: 20px 0;
                text-align: center;
                color: #333;
                transition: all 0.3s ease;
            }
            .feature-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            }
            .feature-icon {
                font-size: 3rem;
                margin-bottom: 20px;
                color: #2E7D32;
            }
        </style>
    </head>
    <body>
        <div class="hero-section">
            <div class="container">
                <div class="hero-content mx-auto">
                    <i class="fas fa-mobile-alt hero-icon"></i>
                    <h1 class="fw-bold mb-4">منصة تعبئة الرصيد اليمنية</h1>
                    <p class="lead mb-4">
                        منصة شاملة لتعبئة رصيد الهاتف، شراء الباقات، ودفع الفواتير
                        <br>
                        آمنة، سريعة، ومتاحة 24/7
                    </p>
                    <div class="d-flex flex-wrap justify-content-center">
                        <a href="/login" class="btn-primary-custom">
                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                        </a>
                        <a href="/login" class="btn-primary-custom">
                            <i class="fas fa-user-plus me-2"></i>إنشاء حساب
                        </a>
                    </div>
                </div>
                
                <div class="row mt-5">
                    <div class="col-md-4">
                        <div class="feature-card">
                            <i class="fas fa-shield-alt feature-icon"></i>
                            <h5 class="fw-bold">آمن ومحمي</h5>
                            <p>جميع المعاملات محمية بأحدث تقنيات الأمان والتشفير</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="feature-card">
                            <i class="fas fa-bolt feature-icon"></i>
                            <h5 class="fw-bold">سريع وفوري</h5>
                            <p>معالجة فورية لجميع العمليات خلال ثوانٍ معدودة</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="feature-card">
                            <i class="fas fa-clock feature-icon"></i>
                            <h5 class="fw-bold">متاح 24/7</h5>
                            <p>خدمة متواصلة على مدار الساعة طوال أيام الأسبوع</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    '''

if __name__ == '__main__':
    print("🚀 بدء تشغيل منصة تعبئة الرصيد اليمنية...")
    print("📱 الخدمات المتاحة:")
    print("   - شحن رصيد الهاتف")
    print("   - شراء الباقات")
    print("   - دفع الفواتير")
    print("   - تحويل الأموال")
    print("\n🔐 حسابات تجريبية:")
    print("   مدير: admin / admin123")
    print("   عميل: customer1 / 123456")
    print("   وكيل: agent1 / agent123")
    print("\n🌐 الرابط: http://localhost:5000")
    print("=" * 50)
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        use_reloader=True,
        threaded=True
    )
