flowchart TD
    Start([المستخدم يريد شحن رصيد]) --> Login{هل المستخدم مسجل دخول؟}
    
    Login -->|لا| LoginPage[صفحة تسجيل الدخول]
    LoginPage --> AuthCheck{تسجيل دخول صحيح؟}
    AuthCheck -->|لا| LoginError[عرض رسالة خطأ]
    LoginError --> LoginPage
    AuthCheck -->|نعم| Dashboard
    
    Login -->|نعم| Dashboard[لوحة التحكم الرئيسية]
    
    Dashboard --> RechargeBtn[اضغط على "شحن رصيد"]
    RechargeBtn --> SelectProvider[اختيار شركة الاتصالات]
    
    SelectProvider --> EnterPhone[إدخال رقم الهاتف]
    EnterPhone --> ValidatePhone{رقم صحيح؟}
    ValidatePhone -->|لا| PhoneError[عرض رسالة خطأ]
    PhoneError --> EnterPhone
    
    ValidatePhone -->|نعم| SelectAmount[اختيار مبلغ الشحن]
    SelectAmount --> CheckBalance{رصيد كافي؟}
    CheckBalance -->|لا| InsufficientBalance[رصيد غير كافي<br/>اقتراح إضافة رصيد]
    InsufficientBalance --> AddBalance[إضافة رصيد للمحفظة]
    AddBalance --> SelectAmount
    
    CheckBalance -->|نعم| Summary[عرض ملخص العملية]
    Summary --> Confirm{تأكيد العملية؟}
    Confirm -->|لا| SelectAmount
    
    Confirm -->|نعم| Processing[معالجة العملية]
    Processing --> APICall[استدعاء API الخارجية]
    
    APICall --> APIResponse{استجابة API}
    APIResponse -->|نجاح فوري| Success[✅ نجحت العملية]
    APIResponse -->|قيد المعالجة| Pending[⏳ العملية قيد المعالجة]
    APIResponse -->|فشل| Failed[❌ فشلت العملية]
    
    Pending --> BackgroundCheck[فحص دوري للحالة]
    BackgroundCheck --> FinalResult{النتيجة النهائية}
    FinalResult -->|نجاح| Success
    FinalResult -->|فشل| Failed
    
    Success --> Notification[إرسال إشعار نجاح]
    Failed --> RefundBalance[إرجاع المبلغ للمحفظة]
    RefundBalance --> FailNotification[إرسال إشعار فشل]
    
    Notification --> End([انتهاء العملية])
    FailNotification --> End
    
    %% الألوان
    classDef startEnd fill:#e1f5fe
    classDef process fill:#f3e5f5
    classDef decision fill:#fff3e0
    classDef success fill:#e8f5e8
    classDef error fill:#ffebee
    classDef warning fill:#fff8e1
    
    class Start,End startEnd
    class Dashboard,RechargeBtn,SelectProvider,EnterPhone,SelectAmount,Summary,Processing,APICall,BackgroundCheck,Notification,AddBalance process
    class Login,AuthCheck,ValidatePhone,CheckBalance,Confirm,APIResponse,FinalResult decision
    class Success success
    class LoginError,PhoneError,Failed,RefundBalance,FailNotification error
    class InsufficientBalance,Pending warning
