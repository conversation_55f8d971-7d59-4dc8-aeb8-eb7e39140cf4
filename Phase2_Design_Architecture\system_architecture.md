# تصميم البنية المعمارية - مشروع تعبئة الرصيد

## 🏗️ نظرة عامة على البنية المعمارية

### نمط البنية المختار: Layered Architecture + Microservices Ready
```
┌─────────────────────────────────────────────────────┐
│                Presentation Layer                   │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐   │
│  │   Web App   │ │ Mobile App  │ │ Admin Panel │   │
│  │  (Next.js)  │ │(React Native│ │  (Django)   │   │
│  └─────────────┘ └─────────────┘ └─────────────┘   │
├─────────────────────────────────────────────────────┤
│                   API Gateway                       │
│              (Django + DRF + JWT)                   │
├─────────────────────────────────────────────────────┤
│                Business Logic Layer                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐   │
│  │    Auth     │ │ Transaction │ │   Wallet    │   │
│  │   Service   │ │   Service   │ │   Service   │   │
│  └─────────────┘ └─────────────┘ └─────────────┘   │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐   │
│  │   Agent     │ │Notification │ │   Report    │   │
│  │   Service   │ │   Service   │ │   Service   │   │
│  └─────────────┘ └─────────────┘ └─────────────┘   │
├─────────────────────────────────────────────────────┤
│                  Data Access Layer                  │
│              (Django ORM + Repository)              │
├─────────────────────────────────────────────────────┤
│                   Data Layer                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐   │
│  │ PostgreSQL  │ │    Redis    │ │   File      │   │
│  │  Database   │ │    Cache    │ │  Storage    │   │
│  └─────────────┘ └─────────────┘ └─────────────┘   │
└─────────────────────────────────────────────────────┘
```

## 🔧 المكونات الأساسية للنظام

### 1. طبقة العرض (Presentation Layer)

#### أ) تطبيق الويب (Web Application)
```javascript
// هيكل مشروع Next.js
src/
├── app/                    # App Router (Next.js 13+)
│   ├── (auth)/
│   │   ├── login/
│   │   └── register/
│   ├── dashboard/
│   ├── recharge/
│   ├── packages/
│   └── transactions/
├── components/
│   ├── ui/                 # مكونات UI أساسية
│   ├── forms/              # نماذج الإدخال
│   ├── layouts/            # تخطيطات الصفحات
│   └── features/           # مكونات الميزات
├── hooks/                  # React Hooks مخصصة
├── stores/                 # إدارة الحالة (Zustand)
├── utils/                  # وظائف مساعدة
└── types/                  # TypeScript types
```

#### ب) التطبيق المحمول (Mobile Application)
```javascript
// هيكل مشروع React Native
src/
├── screens/
│   ├── Auth/
│   ├── Dashboard/
│   ├── Recharge/
│   └── Profile/
├── components/
│   ├── common/
│   ├── forms/
│   └── navigation/
├── services/               # API calls
├── stores/                 # State management
├── utils/
└── assets/
```

#### ج) لوحة الإدارة (Admin Panel)
```python
# Django Admin مخصص
admin/
├── templates/
│   └── admin/
│       ├── dashboard.html
│       ├── users/
│       ├── transactions/
│       └── reports/
├── views.py
├── urls.py
└── forms.py
```

### 2. طبقة API Gateway

#### Django REST Framework Configuration
```python
# api/urls.py
from django.urls import path, include
from rest_framework.routers import DefaultRouter

router = DefaultRouter()
router.register(r'users', UserViewSet)
router.register(r'transactions', TransactionViewSet)
router.register(r'wallets', WalletViewSet)

urlpatterns = [
    path('api/v1/', include(router.urls)),
    path('api/v1/auth/', include('apps.authentication.urls')),
    path('api/v1/recharge/', include('apps.transactions.urls')),
    path('api/v1/agents/', include('apps.agents.urls')),
]
```

#### API Versioning Strategy
```python
# settings.py
REST_FRAMEWORK = {
    'DEFAULT_VERSIONING_CLASS': 'rest_framework.versioning.URLPathVersioning',
    'DEFAULT_VERSION': 'v1',
    'ALLOWED_VERSIONS': ['v1', 'v2'],
    'VERSION_PARAM': 'version',
}
```

### 3. طبقة منطق الأعمال (Business Logic Layer)

#### أ) خدمة المصادقة (Authentication Service)
```python
# apps/authentication/services.py
class AuthenticationService:
    def __init__(self):
        self.token_service = TokenService()
        self.user_service = UserService()
    
    def login(self, phone_number: str, password: str) -> dict:
        user = self.user_service.authenticate(phone_number, password)
        if user:
            tokens = self.token_service.generate_tokens(user)
            return {
                'user': user,
                'access_token': tokens['access'],
                'refresh_token': tokens['refresh']
            }
        raise AuthenticationError("Invalid credentials")
    
    def verify_2fa(self, user_id: int, code: str) -> bool:
        return self.user_service.verify_2fa_code(user_id, code)
```

#### ب) خدمة المعاملات (Transaction Service)
```python
# apps/transactions/services.py
class TransactionService:
    def __init__(self):
        self.wallet_service = WalletService()
        self.api_service = ExternalAPIService()
        self.notification_service = NotificationService()
    
    def create_recharge_transaction(self, user_id: int, phone_number: str, 
                                  amount: Decimal, provider: str) -> Transaction:
        # 1. التحقق من الرصيد
        wallet = self.wallet_service.get_user_wallet(user_id)
        if wallet.balance < amount:
            raise InsufficientBalanceError()
        
        # 2. إنشاء المعاملة
        transaction = Transaction.objects.create(
            user_id=user_id,
            target_number=phone_number,
            amount=amount,
            provider=provider,
            status='pending'
        )
        
        # 3. خصم المبلغ من المحفظة
        self.wallet_service.debit_wallet(wallet, amount, transaction.id)
        
        # 4. استدعاء API الخارجية
        self._process_external_api_call.delay(transaction.id)
        
        return transaction
    
    @celery_app.task
    def _process_external_api_call(self, transaction_id: int):
        transaction = Transaction.objects.get(id=transaction_id)
        try:
            result = self.api_service.recharge_phone(
                transaction.target_number,
                transaction.amount,
                transaction.provider
            )
            self._handle_api_response(transaction, result)
        except Exception as e:
            self._handle_api_error(transaction, e)
```

#### ج) خدمة المحافظ (Wallet Service)
```python
# apps/wallets/services.py
class WalletService:
    def __init__(self):
        self.encryption_service = EncryptionService()
    
    def get_user_wallet(self, user_id: int) -> Wallet:
        wallet, created = Wallet.objects.get_or_create(
            user_id=user_id,
            defaults={'balance': Decimal('0.00')}
        )
        return wallet
    
    def credit_wallet(self, wallet: Wallet, amount: Decimal, 
                     reference: str = None) -> WalletTransaction:
        with transaction.atomic():
            old_balance = wallet.balance
            wallet.balance += amount
            wallet.save()
            
            return WalletTransaction.objects.create(
                wallet=wallet,
                transaction_type='credit',
                amount=amount,
                balance_before=old_balance,
                balance_after=wallet.balance,
                reference=reference
            )
    
    def debit_wallet(self, wallet: Wallet, amount: Decimal, 
                    reference: str = None) -> WalletTransaction:
        with transaction.atomic():
            if wallet.balance < amount:
                raise InsufficientBalanceError()
            
            old_balance = wallet.balance
            wallet.balance -= amount
            wallet.save()
            
            return WalletTransaction.objects.create(
                wallet=wallet,
                transaction_type='debit',
                amount=amount,
                balance_before=old_balance,
                balance_after=wallet.balance,
                reference=reference
            )
```

### 4. طبقة الوصول للبيانات (Data Access Layer)

#### Repository Pattern Implementation
```python
# core/repositories/base.py
from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any

class BaseRepository(ABC):
    def __init__(self, model):
        self.model = model
    
    def get_by_id(self, id: int) -> Optional[Any]:
        try:
            return self.model.objects.get(id=id)
        except self.model.DoesNotExist:
            return None
    
    def get_all(self) -> List[Any]:
        return list(self.model.objects.all())
    
    def create(self, **kwargs) -> Any:
        return self.model.objects.create(**kwargs)
    
    def update(self, id: int, **kwargs) -> Optional[Any]:
        obj = self.get_by_id(id)
        if obj:
            for key, value in kwargs.items():
                setattr(obj, key, value)
            obj.save()
        return obj
    
    def delete(self, id: int) -> bool:
        obj = self.get_by_id(id)
        if obj:
            obj.delete()
            return True
        return False

# apps/transactions/repositories.py
class TransactionRepository(BaseRepository):
    def __init__(self):
        super().__init__(Transaction)
    
    def get_user_transactions(self, user_id: int, 
                            status: str = None) -> List[Transaction]:
        queryset = self.model.objects.filter(user_id=user_id)
        if status:
            queryset = queryset.filter(status=status)
        return list(queryset.order_by('-created_at'))
    
    def get_pending_transactions(self) -> List[Transaction]:
        return list(self.model.objects.filter(
            status__in=['pending', 'processing']
        ))
```

### 5. طبقة البيانات (Data Layer)

#### Database Configuration
```python
# settings/database.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.getenv('DB_NAME', 'recharge_db'),
        'USER': os.getenv('DB_USER', 'postgres'),
        'PASSWORD': os.getenv('DB_PASSWORD'),
        'HOST': os.getenv('DB_HOST', 'localhost'),
        'PORT': os.getenv('DB_PORT', '5432'),
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        },
        'CONN_MAX_AGE': 60,
    }
}

# Connection Pooling
DATABASE_POOL_ARGS = {
    'max_overflow': 10,
    'pool_size': 5,
    'pool_recycle': -1,
    'pool_timeout': 30,
}
```

#### Caching Strategy
```python
# settings/cache.py
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.getenv('REDIS_URL', 'redis://127.0.0.1:6379/1'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'SERIALIZER': 'django_redis.serializers.json.JSONSerializer',
        },
        'KEY_PREFIX': 'recharge_app',
        'TIMEOUT': 300,  # 5 minutes default
    },
    'sessions': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.getenv('REDIS_URL', 'redis://127.0.0.1:6379/2'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        },
        'KEY_PREFIX': 'sessions',
    }
}

# Cache decorators
from django.core.cache import cache

def cache_user_balance(user_id: int) -> Decimal:
    cache_key = f"user_balance_{user_id}"
    balance = cache.get(cache_key)
    if balance is None:
        wallet = Wallet.objects.get(user_id=user_id)
        balance = wallet.balance
        cache.set(cache_key, balance, timeout=300)  # 5 minutes
    return balance
```

## 🔄 أنماط التصميم المستخدمة

### 1. Repository Pattern
```python
# تطبيق نمط Repository لفصل منطق الوصول للبيانات
class UserRepository:
    def find_by_phone(self, phone_number: str) -> Optional[User]:
        return User.objects.filter(phone_number=phone_number).first()

    def find_active_agents(self) -> QuerySet[User]:
        return User.objects.filter(
            user_type='agent',
            is_active=True,
            agents__status='approved'
        )
```

### 2. Service Layer Pattern
```python
# فصل منطق الأعمال في طبقة الخدمات
class RechargeService:
    def __init__(self):
        self.transaction_repo = TransactionRepository()
        self.wallet_repo = WalletRepository()
        self.api_client = ExternalAPIClient()

    def process_recharge(self, request_data: dict) -> dict:
        # منطق معالجة الشحن
        pass
```

### 3. Factory Pattern
```python
# إنشاء كائنات API حسب نوع المزود
class APIClientFactory:
    @staticmethod
    def create_client(provider: str) -> BaseAPIClient:
        clients = {
            'yemen_mobile': YemenMobileAPIClient,
            'mtn': MTNAPIClient,
            'sabaphone': SabaphoneAPIClient,
        }
        client_class = clients.get(provider)
        if not client_class:
            raise ValueError(f"Unsupported provider: {provider}")
        return client_class()
```

### 4. Observer Pattern
```python
# نظام الإشعارات باستخدام Django Signals
from django.db.models.signals import post_save
from django.dispatch import receiver

@receiver(post_save, sender=Transaction)
def transaction_status_changed(sender, instance, created, **kwargs):
    if not created and instance.status == 'success':
        NotificationService.send_success_notification(instance)
    elif not created and instance.status == 'failed':
        NotificationService.send_failure_notification(instance)
```

## 🔐 أمان البنية المعمارية

### 1. طبقات الحماية
```
Internet → WAF → Load Balancer → API Gateway → Services → Database
    ↓        ↓         ↓            ↓           ↓         ↓
  DDoS    Rate      SSL/TLS      JWT Auth   Input     Encryption
Protection Limiting Termination Validation Validation at Rest
```

### 2. تطبيق مبدأ Zero Trust
```python
# كل طلب يتم التحقق منه
class SecurityMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # 1. التحقق من IP
        if not self.is_allowed_ip(request.META.get('REMOTE_ADDR')):
            return HttpResponseForbidden()

        # 2. التحقق من Rate Limiting
        if not self.check_rate_limit(request):
            return HttpResponseTooManyRequests()

        # 3. التحقق من JWT Token
        if not self.validate_token(request):
            return HttpResponseUnauthorized()

        response = self.get_response(request)
        return response
```

### 3. تشفير البيانات الحساسة
```python
class EncryptionService:
    def __init__(self):
        self.cipher = Fernet(settings.ENCRYPTION_KEY)

    def encrypt_sensitive_data(self, data: dict) -> dict:
        sensitive_fields = ['phone_number', 'national_id', 'account_number']
        encrypted_data = data.copy()

        for field in sensitive_fields:
            if field in encrypted_data:
                encrypted_data[field] = self.cipher.encrypt(
                    str(encrypted_data[field]).encode()
                ).decode()

        return encrypted_data
```

## 📊 مراقبة وتسجيل الأحداث

### 1. Structured Logging
```python
import structlog

logger = structlog.get_logger()

class TransactionService:
    def create_transaction(self, user_id: int, amount: Decimal):
        logger.info(
            "transaction_creation_started",
            user_id=user_id,
            amount=str(amount),
            timestamp=timezone.now().isoformat()
        )

        try:
            transaction = self._create_transaction(user_id, amount)
            logger.info(
                "transaction_created_successfully",
                transaction_id=transaction.id,
                user_id=user_id,
                amount=str(amount)
            )
            return transaction
        except Exception as e:
            logger.error(
                "transaction_creation_failed",
                user_id=user_id,
                amount=str(amount),
                error=str(e),
                exc_info=True
            )
            raise
```

### 2. Health Checks
```python
# health/views.py
from django.http import JsonResponse
from django.db import connection

def health_check(request):
    checks = {
        'database': check_database(),
        'redis': check_redis(),
        'external_apis': check_external_apis(),
        'disk_space': check_disk_space(),
    }

    all_healthy = all(checks.values())
    status_code = 200 if all_healthy else 503

    return JsonResponse({
        'status': 'healthy' if all_healthy else 'unhealthy',
        'checks': checks,
        'timestamp': timezone.now().isoformat()
    }, status=status_code)

def check_database():
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        return True
    except Exception:
        return False
```

## 🚀 قابلية التوسع والأداء

### 1. Database Optimization
```python
# تحسين الاستعلامات
class TransactionQuerySet(models.QuerySet):
    def with_related(self):
        return self.select_related('user', 'provider').prefetch_related('attempts')

    def recent(self, days=30):
        cutoff_date = timezone.now() - timedelta(days=days)
        return self.filter(created_at__gte=cutoff_date)

class Transaction(models.Model):
    objects = TransactionQuerySet.as_manager()

    class Meta:
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['status', 'created_at']),
            models.Index(fields=['provider', 'created_at']),
        ]
```

### 2. Caching Strategy
```python
# Multi-level caching
class CacheService:
    def __init__(self):
        self.l1_cache = caches['default']  # Redis
        self.l2_cache = caches['memcached']  # Memcached

    def get_user_data(self, user_id: int):
        # L1 Cache (Redis)
        cache_key = f"user_data_{user_id}"
        data = self.l1_cache.get(cache_key)

        if data is None:
            # L2 Cache (Memcached)
            data = self.l2_cache.get(cache_key)

            if data is None:
                # Database
                data = self._fetch_from_database(user_id)
                self.l2_cache.set(cache_key, data, timeout=3600)

            self.l1_cache.set(cache_key, data, timeout=300)

        return data
```

### 3. Asynchronous Processing
```python
# معالجة غير متزامنة للمهام الثقيلة
@celery_app.task(bind=True, max_retries=3)
def process_bulk_transactions(self, transaction_ids: List[int]):
    try:
        for transaction_id in transaction_ids:
            process_single_transaction.delay(transaction_id)
    except Exception as exc:
        logger.error(f"Bulk processing failed: {exc}")
        raise self.retry(exc=exc, countdown=60)

@celery_app.task(bind=True, max_retries=5)
def process_single_transaction(self, transaction_id: int):
    try:
        transaction = Transaction.objects.get(id=transaction_id)
        api_client = APIClientFactory.create_client(transaction.provider)
        result = api_client.process_transaction(transaction)
        transaction.update_status(result)
    except Exception as exc:
        logger.error(f"Transaction {transaction_id} failed: {exc}")
        raise self.retry(exc=exc, countdown=30)
```

## 🔄 إدارة الحالة والتزامن

### 1. Database Transactions
```python
from django.db import transaction

class WalletService:
    @transaction.atomic
    def transfer_funds(self, from_wallet_id: int, to_wallet_id: int, amount: Decimal):
        # استخدام select_for_update لمنع Race Conditions
        from_wallet = Wallet.objects.select_for_update().get(id=from_wallet_id)
        to_wallet = Wallet.objects.select_for_update().get(id=to_wallet_id)

        if from_wallet.balance < amount:
            raise InsufficientBalanceError()

        from_wallet.balance -= amount
        to_wallet.balance += amount

        from_wallet.save()
        to_wallet.save()

        # تسجيل المعاملات
        self._create_wallet_transactions(from_wallet, to_wallet, amount)
```

### 2. Distributed Locks
```python
import redis
from contextlib import contextmanager

class DistributedLock:
    def __init__(self, redis_client, key: str, timeout: int = 10):
        self.redis_client = redis_client
        self.key = f"lock:{key}"
        self.timeout = timeout

    @contextmanager
    def acquire(self):
        identifier = str(uuid.uuid4())
        acquired = self.redis_client.set(
            self.key, identifier, nx=True, ex=self.timeout
        )

        if not acquired:
            raise LockAcquisitionError(f"Could not acquire lock: {self.key}")

        try:
            yield
        finally:
            # تحرير القفل بأمان
            lua_script = """
            if redis.call("get", KEYS[1]) == ARGV[1] then
                return redis.call("del", KEYS[1])
            else
                return 0
            end
            """
            self.redis_client.eval(lua_script, 1, self.key, identifier)

# الاستخدام
def process_critical_operation(user_id: int):
    lock = DistributedLock(redis_client, f"user_operation_{user_id}")
    with lock.acquire():
        # العملية الحرجة هنا
        pass
```

هذا التصميم المعماري الشامل يوفر أساساً قوياً ومرناً وقابلاً للتوسع لبناء منصة تعبئة الرصيد بأعلى معايير الجودة والأمان والأداء.
