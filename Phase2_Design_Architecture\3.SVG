<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 2624.859375 1879.6309814453125" style="max-width: 2624.859375px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef"><style>#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .error-icon{fill:#a44141;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .error-text{fill:#ddd;stroke:#ddd;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .edge-thickness-normal{stroke-width:1px;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .edge-thickness-thick{stroke-width:3.5px;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .edge-pattern-solid{stroke-dasharray:0;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .marker.cross{stroke:lightgrey;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef p{margin:0;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .cluster-label text{fill:#F9FFFE;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .cluster-label span{color:#F9FFFE;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .cluster-label span p{background-color:transparent;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .label text,#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef span{fill:#ccc;color:#ccc;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .node rect,#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .node circle,#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .node ellipse,#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .node polygon,#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .rough-node .label text,#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .node .label text,#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .image-shape .label,#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .icon-shape .label{text-anchor:middle;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .rough-node .label,#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .node .label,#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .image-shape .label,#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .icon-shape .label{text-align:center;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .node.clickable{cursor:pointer;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .arrowheadPath{fill:lightgrey;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .cluster text{fill:#F9FFFE;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .cluster span{color:#F9FFFE;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef rect.text{fill:none;stroke-width:0;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .icon-shape,#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .icon-shape p,#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .icon-shape rect,#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .userClass&gt;*{fill:#e1f5fe!important;stroke:#01579b!important;stroke-width:2px!important;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .userClass span{fill:#e1f5fe!important;stroke:#01579b!important;stroke-width:2px!important;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .presentationClass&gt;*{fill:#f3e5f5!important;stroke:#4a148c!important;stroke-width:2px!important;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .presentationClass span{fill:#f3e5f5!important;stroke:#4a148c!important;stroke-width:2px!important;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .infrastructureClass&gt;*{fill:#fff3e0!important;stroke:#e65100!important;stroke-width:2px!important;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .infrastructureClass span{fill:#fff3e0!important;stroke:#e65100!important;stroke-width:2px!important;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .apiClass&gt;*{fill:#e8f5e8!important;stroke:#1b5e20!important;stroke-width:2px!important;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .apiClass span{fill:#e8f5e8!important;stroke:#1b5e20!important;stroke-width:2px!important;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .businessClass&gt;*{fill:#fff8e1!important;stroke:#f57f17!important;stroke-width:2px!important;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .businessClass span{fill:#fff8e1!important;stroke:#f57f17!important;stroke-width:2px!important;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .dataClass&gt;*{fill:#fce4ec!important;stroke:#880e4f!important;stroke-width:2px!important;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .dataClass span{fill:#fce4ec!important;stroke:#880e4f!important;stroke-width:2px!important;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .externalClass&gt;*{fill:#f1f8e9!important;stroke:#33691e!important;stroke-width:2px!important;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .externalClass span{fill:#f1f8e9!important;stroke:#33691e!important;stroke-width:2px!important;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .monitoringClass&gt;*{fill:#e0f2f1!important;stroke:#00695c!important;stroke-width:2px!important;}#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef .monitoringClass span{fill:#e0f2f1!important;stroke:#00695c!important;stroke-width:2px!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="Monitoring" class="cluster"><rect height="128" width="1345.96484375" y="1743.6309814453125" x="971.7890625" style=""></rect><g transform="translate(1606.904296875, 1743.6309814453125)" class="cluster-label"><foreignObject height="24" width="75.734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Monitoring</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph8" class="cluster"><rect height="104" width="952.734375" y="1258" x="8" style=""></rect><g transform="translate(437.8828125, 1258)" class="cluster-label"><foreignObject height="24" width="92.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>External APIs</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph7" class="cluster"><rect height="152.6309814453125" width="580.640625" y="1541" x="1930.609375" style=""></rect><g transform="translate(2182.359375, 1541)" class="cluster-label"><foreignObject height="24" width="77.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Data Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph6" class="cluster"><rect height="233" width="725.68359375" y="1258" x="1798.94140625" style=""></rect><g transform="translate(2097.767578125, 1258)" class="cluster-label"><foreignObject height="24" width="128.03125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Data Access Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph5" class="cluster"><rect height="233" width="473.3359375" y="1258" x="1165.7109375" style=""></rect><g transform="translate(1321.66796875, 1258)" class="cluster-label"><foreignObject height="24" width="161.421875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Background Processing</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph4" class="cluster"><rect height="104" width="2225.42578125" y="1104" x="391.43359375" style=""></rect><g transform="translate(1431.419921875, 1104)" class="cluster-label"><foreignObject height="24" width="145.453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Business Logic Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph3" class="cluster"><rect height="256" width="2291.6953125" y="798" x="265.73046875" style=""></rect><g transform="translate(1344.4296875, 798)" class="cluster-label"><foreignObject height="24" width="134.296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>API Gateway Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="Infrastructure" class="cluster"><rect height="384" width="607.7265625" y="364" x="1374.56640625" style=""></rect><g transform="translate(1628.6953125, 364)" class="cluster-label"><foreignObject height="24" width="99.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Infrastructure</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="128" width="661.9375" y="186" x="1304.9453125" style=""></rect><g transform="translate(1568.8046875, 186)" class="cluster-label"><foreignObject height="24" width="134.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Presentation Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="128" width="628.34375" y="8" x="1317.96875" style=""></rect><g transform="translate(1590.921875, 8)" class="cluster-label"><foreignObject height="24" width="82.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Users Layer</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Customer_WebApp_0" d="M1585.867,92.103L1561.408,99.42C1536.948,106.736,1488.029,121.368,1463.569,132.851C1439.109,144.333,1439.109,152.667,1439.109,161C1439.109,169.333,1439.109,177.667,1456.67,187.628C1474.231,197.588,1509.353,209.177,1526.914,214.971L1544.475,220.765"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Customer_MobileApp_1" d="M1653.078,111L1653.078,115.167C1653.078,119.333,1653.078,127.667,1653.078,136C1653.078,144.333,1653.078,152.667,1653.078,161C1653.078,169.333,1653.078,177.667,1671.578,187.849C1690.078,198.032,1727.079,210.065,1745.579,216.081L1764.079,222.097"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Agent_WebApp_2" d="M1788.453,90.134L1762.557,97.778C1736.661,105.423,1684.87,120.711,1658.974,132.522C1633.078,144.333,1633.078,152.667,1633.078,161C1633.078,169.333,1633.078,177.667,1633.078,185.333C1633.078,193,1633.078,200,1633.078,203.5L1633.078,207"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Agent_MobileApp_3" d="M1849.883,111L1849.883,115.167C1849.883,119.333,1849.883,127.667,1849.883,136C1849.883,144.333,1849.883,152.667,1849.883,161C1849.883,169.333,1849.883,177.667,1849.883,185.333C1849.883,193,1849.883,200,1849.883,203.5L1849.883,207"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Admin_AdminPanel_4" d="M1419.109,111L1419.109,115.167C1419.109,119.333,1419.109,127.667,1419.109,136C1419.109,144.333,1419.109,152.667,1419.109,161C1419.109,169.333,1419.109,177.667,1419.109,185.333C1419.109,193,1419.109,200,1419.109,203.5L1419.109,207"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WebApp_CDN_5" d="M1633.078,289L1633.078,293.167C1633.078,297.333,1633.078,305.667,1633.078,314C1633.078,322.333,1633.078,330.667,1633.078,339C1633.078,347.333,1633.078,355.667,1645.616,365.244C1658.153,374.821,1683.229,385.641,1695.766,391.051L1708.304,396.462"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MobileApp_CDN_6" d="M1849.883,289L1849.883,293.167C1849.883,297.333,1849.883,305.667,1849.883,314C1849.883,322.333,1849.883,330.667,1849.883,339C1849.883,347.333,1849.883,355.667,1845.911,363.545C1841.939,371.423,1833.995,378.846,1830.023,382.558L1826.051,386.269"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AdminPanel_LB_7" d="M1419.109,289L1419.109,293.167C1419.109,297.333,1419.109,305.667,1419.109,314C1419.109,322.333,1419.109,330.667,1419.109,339C1419.109,347.333,1419.109,355.667,1419.109,370.5C1419.109,385.333,1419.109,406.667,1419.109,428C1419.109,449.333,1419.109,470.667,1419.109,492C1419.109,513.333,1419.109,534.667,1419.109,556C1419.109,577.333,1419.109,598.667,1445.361,615.914C1471.613,633.162,1524.116,646.324,1550.368,652.905L1576.62,659.486"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CDN_WAF_8" d="M1781.391,467L1781.391,471.167C1781.391,475.333,1781.391,483.667,1781.391,491.333C1781.391,499,1781.391,506,1781.391,509.5L1781.391,513"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WAF_LB_9" d="M1781.391,595L1781.391,599.167C1781.391,603.333,1781.391,611.667,1774.998,619.658C1768.605,627.649,1755.819,635.298,1749.426,639.122L1743.033,642.947"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LB_APIGateway_10" d="M1674.406,723L1674.406,727.167C1674.406,731.333,1674.406,739.667,1674.406,748C1674.406,756.333,1674.406,764.667,1674.406,773C1674.406,781.333,1674.406,789.667,1674.406,797.333C1674.406,805,1674.406,812,1674.406,815.5L1674.406,819"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_APIGateway_Auth_11" d="M1588.633,883.047L1559.458,890.206C1530.283,897.364,1471.932,911.682,1442.757,922.341C1413.582,933,1413.582,940,1413.582,943.5L1413.582,947"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_APIGateway_RateLimit_12" d="M1588.633,866.502L1399.723,876.419C1210.813,886.335,832.992,906.167,644.082,919.584C455.172,933,455.172,940,455.172,943.5L455.172,947"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Auth_AuthService_13" d="M1413.582,1029L1413.582,1033.167C1413.582,1037.333,1413.582,1045.667,1413.582,1054C1413.582,1062.333,1413.582,1070.667,1413.582,1079C1413.582,1087.333,1413.582,1095.667,1413.582,1103.333C1413.582,1111,1413.582,1118,1413.582,1121.5L1413.582,1125"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_APIGateway_TransactionService_14" d="M1588.633,869.947L1487.798,879.289C1386.964,888.631,1185.294,907.316,1084.46,927.324C983.625,947.333,983.625,968.667,983.625,990C983.625,1011.333,983.625,1032.667,983.625,1047.5C983.625,1062.333,983.625,1070.667,983.625,1079C983.625,1087.333,983.625,1095.667,983.625,1103.333C983.625,1111,983.625,1118,983.625,1121.5L983.625,1125"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_APIGateway_WalletService_15" d="M1760.18,878.084L1802.77,886.07C1845.359,894.056,1930.539,910.028,1973.129,928.681C2015.719,947.333,2015.719,968.667,2015.719,990C2015.719,1011.333,2015.719,1032.667,2015.719,1047.5C2015.719,1062.333,2015.719,1070.667,2015.719,1079C2015.719,1087.333,2015.719,1095.667,2015.719,1103.333C2015.719,1111,2015.719,1118,2015.719,1121.5L2015.719,1125"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_APIGateway_AgentService_16" d="M1760.18,871.531L1841.878,880.609C1923.576,889.687,2086.971,907.844,2168.669,927.589C2250.367,947.333,2250.367,968.667,2250.367,990C2250.367,1011.333,2250.367,1032.667,2250.367,1047.5C2250.367,1062.333,2250.367,1070.667,2250.367,1079C2250.367,1087.333,2250.367,1095.667,2250.367,1103.333C2250.367,1111,2250.367,1118,2250.367,1121.5L2250.367,1125"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_APIGateway_NotificationService_17" d="M1668.948,901L1668.365,905.167C1667.782,909.333,1666.615,917.667,1666.032,932.5C1665.449,947.333,1665.449,968.667,1665.449,990C1665.449,1011.333,1665.449,1032.667,1665.449,1047.5C1665.449,1062.333,1665.449,1070.667,1665.449,1079C1665.449,1087.333,1665.449,1095.667,1665.449,1103.333C1665.449,1111,1665.449,1118,1665.449,1121.5L1665.449,1125"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_APIGateway_ReportService_18" d="M1760.18,868.755L1881.323,878.296C2002.466,887.837,2244.753,906.918,2365.896,927.126C2487.039,947.333,2487.039,968.667,2487.039,990C2487.039,1011.333,2487.039,1032.667,2487.039,1047.5C2487.039,1062.333,2487.039,1070.667,2487.039,1079C2487.039,1087.333,2487.039,1095.667,2487.039,1103.333C2487.039,1111,2487.039,1118,2487.039,1121.5L2487.039,1125"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TransactionService_Queue_19" d="M1096.102,1177.803L1122.064,1182.836C1148.026,1187.869,1199.951,1197.934,1225.913,1207.134C1251.875,1216.333,1251.875,1224.667,1251.875,1233C1251.875,1241.333,1251.875,1249.667,1254.698,1257.473C1257.522,1265.28,1263.169,1272.56,1265.992,1276.199L1268.816,1279.839"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NotificationService_Queue_20" d="M1554.904,1183L1537.844,1187.167C1520.785,1191.333,1486.666,1199.667,1469.606,1208C1452.547,1216.333,1452.547,1224.667,1452.547,1233C1452.547,1241.333,1452.547,1249.667,1440.334,1257.794C1428.12,1265.922,1403.694,1273.844,1391.48,1277.805L1379.267,1281.766"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Queue_Celery_21" d="M1292.211,1337L1292.211,1341.167C1292.211,1345.333,1292.211,1353.667,1292.211,1362C1292.211,1370.333,1292.211,1378.667,1293.525,1386.375C1294.84,1394.083,1297.469,1401.167,1298.784,1404.708L1300.098,1408.25"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Scheduler_Celery_22" d="M1512.883,1337L1512.883,1341.167C1512.883,1345.333,1512.883,1353.667,1512.883,1362C1512.883,1370.333,1512.883,1378.667,1496.188,1387.144C1479.493,1395.622,1446.103,1404.245,1429.408,1408.556L1412.713,1412.867"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AuthService_Repository_23" d="M1501.426,1165.57L1566.339,1172.641C1631.253,1179.713,1761.079,1193.857,1825.993,1205.095C1890.906,1216.333,1890.906,1224.667,1890.906,1233C1890.906,1241.333,1890.906,1249.667,1905.088,1257.82C1919.269,1265.973,1947.633,1273.945,1961.814,1277.931L1975.996,1281.918"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TransactionService_Repository_24" d="M1096.102,1162.044L1238.641,1169.703C1381.181,1177.363,1666.26,1192.681,1808.8,1204.507C1951.34,1216.333,1951.34,1224.667,1951.34,1233C1951.34,1241.333,1951.34,1249.667,1960.706,1257.743C1970.071,1265.82,1988.803,1273.639,1998.169,1277.549L2007.534,1281.459"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_WalletService_Repository_25" d="M1992.676,1183L1989.12,1187.167C1985.564,1191.333,1978.452,1199.667,1974.896,1208C1971.34,1216.333,1971.34,1224.667,1971.34,1233C1971.34,1241.333,1971.34,1249.667,1979.121,1257.703C1986.903,1265.74,2002.466,1273.479,2010.247,1277.349L2018.029,1281.219"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AgentService_Repository_26" d="M2195.223,1183L2186.713,1187.167C2178.204,1191.333,2161.184,1199.667,2152.674,1208C2144.164,1216.333,2144.164,1224.667,2144.164,1233C2144.164,1241.333,2144.164,1249.667,2139.225,1257.596C2134.285,1265.525,2124.407,1273.051,2119.467,1276.813L2114.528,1280.576"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NotificationService_Repository_27" d="M1779.473,1167.889L1843.588,1174.574C1907.703,1181.259,2035.934,1194.63,2100.049,1205.481C2164.164,1216.333,2164.164,1224.667,2164.164,1233C2164.164,1241.333,2164.164,1249.667,2157.666,1257.662C2151.168,1265.657,2138.173,1273.313,2131.675,1277.141L2125.177,1280.97"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReportService_Repository_28" d="M2487.039,1183L2487.039,1187.167C2487.039,1191.333,2487.039,1199.667,2487.039,1208C2487.039,1216.333,2487.039,1224.667,2487.039,1233C2487.039,1241.333,2487.039,1249.667,2437.333,1260.12C2387.626,1270.574,2288.213,1283.147,2238.507,1289.434L2188.8,1295.721"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Repository_ORM_29" d="M2054.997,1337L2051.771,1341.167C2048.545,1345.333,2042.093,1353.667,2038.867,1362C2035.641,1370.333,2035.641,1378.667,2035.641,1386.333C2035.641,1394,2035.641,1401,2035.641,1404.5L2035.641,1408"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Repository_Cache_30" d="M2170.189,1337L2184.74,1341.167C2199.29,1345.333,2228.391,1353.667,2242.942,1362C2257.492,1370.333,2257.492,1378.667,2257.492,1386.333C2257.492,1394,2257.492,1401,2257.492,1404.5L2257.492,1408"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ORM_PostgreSQL_31" d="M2035.641,1466L2035.641,1470.167C2035.641,1474.333,2035.641,1482.667,2035.641,1491C2035.641,1499.333,2035.641,1507.667,2035.641,1516C2035.641,1524.333,2035.641,1532.667,2035.641,1540.333C2035.641,1548,2035.641,1555,2035.641,1558.5L2035.641,1562"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Cache_Redis_32" d="M2257.492,1466L2257.492,1470.167C2257.492,1474.333,2257.492,1482.667,2257.492,1491C2257.492,1499.333,2257.492,1507.667,2257.492,1516C2257.492,1524.333,2257.492,1532.667,2257.492,1540.367C2257.492,1548.068,2257.492,1555.136,2257.492,1558.67L2257.492,1562.204"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TransactionService_YemenMobileAPI_33" d="M871.148,1167.734L806.819,1174.445C742.49,1181.156,613.831,1194.578,493.367,1205.456C372.904,1216.333,260.635,1224.667,204.501,1233C148.367,1241.333,148.367,1249.667,148.367,1257.333C148.367,1265,148.367,1272,148.367,1275.5L148.367,1279"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TransactionService_MTNAPI_34" d="M871.148,1171.599L827.403,1177.666C783.658,1183.733,696.167,1195.866,613.538,1206.1C530.909,1216.333,453.142,1224.667,414.258,1233C375.375,1241.333,375.375,1249.667,375.375,1257.333C375.375,1265,375.375,1272,375.375,1275.5L375.375,1279"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TransactionService_SabaphoneAPI_35" d="M871.148,1178.781L847.105,1183.651C823.061,1188.521,774.974,1198.26,728.415,1207.297C681.857,1216.333,636.827,1224.667,614.312,1233C591.797,1241.333,591.797,1249.667,591.797,1257.333C591.797,1265,591.797,1272,591.797,1275.5L591.797,1279"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TransactionService_YemenNetAPI_36" d="M966.476,1183L963.83,1187.167C961.183,1191.333,955.89,1199.667,933.337,1208C910.784,1216.333,870.97,1224.667,851.063,1233C831.156,1241.333,831.156,1249.667,831.156,1257.333C831.156,1265,831.156,1272,831.156,1275.5L831.156,1279"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_APIGateway_Sentry_37" d="M1588.633,866.083L1378.861,876.069C1169.089,886.055,749.544,906.028,539.772,926.681C330,947.333,330,968.667,330,990C330,1011.333,330,1032.667,330,1047.5C330,1062.333,330,1070.667,330,1079C330,1087.333,330,1095.667,330,1108.5C330,1121.333,330,1138.667,330,1156C330,1173.333,330,1190.667,456.117,1203.5C582.234,1216.333,834.469,1224.667,960.586,1233C1086.703,1241.333,1086.703,1249.667,1086.703,1262.5C1086.703,1275.333,1086.703,1292.667,1086.703,1310C1086.703,1327.333,1086.703,1344.667,1086.703,1357.5C1086.703,1370.333,1086.703,1378.667,1086.703,1391.5C1086.703,1404.333,1086.703,1421.667,1086.703,1439C1086.703,1456.333,1086.703,1473.667,1086.703,1486.5C1086.703,1499.333,1086.703,1507.667,1086.703,1516C1086.703,1524.333,1086.703,1532.667,1086.703,1549.553C1086.703,1566.438,1086.703,1591.877,1086.703,1617.315C1086.703,1642.754,1086.703,1668.192,1086.703,1685.078C1086.703,1701.964,1086.703,1710.298,1086.703,1718.631C1086.703,1726.964,1086.703,1735.298,1086.703,1742.964C1086.703,1750.631,1086.703,1757.631,1086.703,1761.131L1086.703,1764.631"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TransactionService_Prometheus_38" d="M1096.102,1164.659L1189.926,1171.883C1283.75,1179.106,1471.398,1193.553,1565.223,1204.943C1659.047,1216.333,1659.047,1224.667,1659.047,1233C1659.047,1241.333,1659.047,1249.667,1659.047,1262.5C1659.047,1275.333,1659.047,1292.667,1659.047,1310C1659.047,1327.333,1659.047,1344.667,1659.047,1357.5C1659.047,1370.333,1659.047,1378.667,1659.047,1391.5C1659.047,1404.333,1659.047,1421.667,1659.047,1439C1659.047,1456.333,1659.047,1473.667,1659.047,1486.5C1659.047,1499.333,1659.047,1507.667,1659.047,1516C1659.047,1524.333,1659.047,1532.667,1659.047,1549.553C1659.047,1566.438,1659.047,1591.877,1659.047,1617.315C1659.047,1642.754,1659.047,1668.192,1659.047,1685.078C1659.047,1701.964,1659.047,1710.298,1659.047,1718.631C1659.047,1726.964,1659.047,1735.298,1659.047,1742.964C1659.047,1750.631,1659.047,1757.631,1659.047,1761.131L1659.047,1764.631"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PostgreSQL_ELK_39" d="M2035.641,1668.631L2035.641,1672.798C2035.641,1676.964,2035.641,1685.298,2035.641,1693.631C2035.641,1701.964,2035.641,1710.298,2035.641,1718.631C2035.641,1726.964,2035.641,1735.298,2037.907,1743.067C2040.173,1750.836,2044.705,1758.04,2046.972,1761.643L2049.238,1765.245"></path><path marker-end="url(#mermaid-491d5623-f7fb-4cd6-881b-f93a124707ef_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Redis_ELK_40" d="M2257.492,1668.427L2257.492,1672.628C2257.492,1676.829,2257.492,1685.23,2257.492,1693.597C2257.492,1701.964,2257.492,1710.298,2257.492,1718.631C2257.492,1726.964,2257.492,1735.298,2240.57,1745.429C2223.647,1755.56,2189.801,1767.488,2172.879,1773.452L2155.956,1779.417"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(1653.078125, 72)" id="flowchart-Customer-661" class="node default userClass"><rect height="78" width="134.421875" y="-39" x="-67.2109375" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-37.2109375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="74.421875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>👤 العملاء<br>Customers</p></span></div></foreignObject></g></g><g transform="translate(1849.8828125, 72)" id="flowchart-Agent-662" class="node default userClass"><rect height="78" width="122.859375" y="-39" x="-61.4296875" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-31.4296875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="62.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🏪 الوكلاء<br>Agents</p></span></div></foreignObject></g></g><g transform="translate(1419.109375, 72)" id="flowchart-Admin-663" class="node default userClass"><rect height="78" width="132.28125" y="-39" x="-66.140625" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-36.140625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="72.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>👨‍💼 المشرفون<br>Admins</p></span></div></foreignObject></g></g><g transform="translate(1633.078125, 250)" id="flowchart-WebApp-664" class="node default presentationClass"><rect height="78" width="169.609375" y="-39" x="-84.8046875" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-54.8046875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="109.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🌐 تطبيق الويب<br>Next.js + React</p></span></div></foreignObject></g></g><g transform="translate(1849.8828125, 250)" id="flowchart-MobileApp-665" class="node default presentationClass"><rect height="78" width="164" y="-39" x="-82" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-52, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="104"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📱 التطبيق المحمول<br>React Native</p></span></div></foreignObject></g></g><g transform="translate(1419.109375, 250)" id="flowchart-AdminPanel-666" class="node default presentationClass"><rect height="78" width="158.328125" y="-39" x="-79.1640625" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-49.1640625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="98.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>⚙️ لوحة الإدارة<br>Django Admin</p></span></div></foreignObject></g></g><g transform="translate(1781.390625, 428)" id="flowchart-CDN-667" class="node default infrastructureClass"><rect height="78" width="138.828125" y="-39" x="-69.4140625" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-39.4140625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="78.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🌍 CDN<br>CloudFront</p></span></div></foreignObject></g></g><g transform="translate(1674.40625, 684)" id="flowchart-LB-668" class="node default infrastructureClass"><rect height="78" width="187.8125" y="-39" x="-93.90625" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-63.90625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="127.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>⚖️ Load Balancer<br>AWS ALB</p></span></div></foreignObject></g></g><g transform="translate(1781.390625, 556)" id="flowchart-WAF-669" class="node default infrastructureClass"><rect height="78" width="260" y="-39" x="-130" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🛡️ Web Application Firewall</p></span></div></foreignObject></g></g><g transform="translate(1674.40625, 862)" id="flowchart-APIGateway-670" class="node default apiClass"><rect height="78" width="171.546875" y="-39" x="-85.7734375" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-55.7734375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="111.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🚪 API Gateway<br>Django + DRF</p></span></div></foreignObject></g></g><g transform="translate(1413.58203125, 990)" id="flowchart-Auth-671" class="node default apiClass"><rect height="78" width="192.109375" y="-39" x="-96.0546875" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-66.0546875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="132.109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔐 Authentication<br>JWT + 2FA</p></span></div></foreignObject></g></g><g transform="translate(455.171875, 990)" id="flowchart-RateLimit-672" class="node default apiClass"><rect height="78" width="180.34375" y="-39" x="-90.171875" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-60.171875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="120.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>⏱️ Rate Limiting<br>Redis</p></span></div></foreignObject></g></g><g transform="translate(1413.58203125, 1156)" id="flowchart-AuthService-673" class="node default businessClass"><rect height="54" width="175.6875" y="-27" x="-87.84375" style="fill:#fff8e1 !important;stroke:#f57f17 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-57.84375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="115.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔑 Auth Service</p></span></div></foreignObject></g></g><g transform="translate(983.625, 1156)" id="flowchart-TransactionService-674" class="node default businessClass"><rect height="54" width="224.953125" y="-27" x="-112.4765625" style="fill:#fff8e1 !important;stroke:#f57f17 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-82.4765625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="164.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>💳 Transaction Service</p></span></div></foreignObject></g></g><g transform="translate(2015.71875, 1156)" id="flowchart-WalletService-675" class="node default businessClass"><rect height="54" width="185.59375" y="-27" x="-92.796875" style="fill:#fff8e1 !important;stroke:#f57f17 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-62.796875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="125.59375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>💰 Wallet Service</p></span></div></foreignObject></g></g><g transform="translate(2250.3671875, 1156)" id="flowchart-AgentService-676" class="node default businessClass"><rect height="54" width="183.703125" y="-27" x="-91.8515625" style="fill:#fff8e1 !important;stroke:#f57f17 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-61.8515625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="123.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🏪 Agent Service</p></span></div></foreignObject></g></g><g transform="translate(1665.44921875, 1156)" id="flowchart-NotificationService-677" class="node default businessClass"><rect height="54" width="228.046875" y="-27" x="-114.0234375" style="fill:#fff8e1 !important;stroke:#f57f17 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-84.0234375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="168.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📢 Notification Service</p></span></div></foreignObject></g></g><g transform="translate(2487.0390625, 1156)" id="flowchart-ReportService-678" class="node default businessClass"><rect height="54" width="189.640625" y="-27" x="-94.8203125" style="fill:#fff8e1 !important;stroke:#f57f17 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-64.8203125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="129.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📊 Report Service</p></span></div></foreignObject></g></g><g transform="translate(1311.51171875, 1439)" id="flowchart-Celery-679" class="node default businessClass"><rect height="54" width="194.65625" y="-27" x="-97.328125" style="fill:#fff8e1 !important;stroke:#f57f17 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-67.328125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="134.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔄 Celery Workers</p></span></div></foreignObject></g></g><g transform="translate(1512.8828125, 1310)" id="flowchart-Scheduler-680" class="node default businessClass"><rect height="54" width="170" y="-27" x="-85" style="fill:#fff8e1 !important;stroke:#f57f17 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-55, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="110"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>⏰ Celery Beat</p></span></div></foreignObject></g></g><g transform="translate(1292.2109375, 1310)" id="flowchart-Queue-681" class="node default businessClass"><rect height="54" width="171.34375" y="-27" x="-85.671875" style="fill:#fff8e1 !important;stroke:#f57f17 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-55.671875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="111.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📋 Redis Queue</p></span></div></foreignObject></g></g><g transform="translate(2075.90234375, 1310)" id="flowchart-Repository-682" class="node default businessClass"><rect height="54" width="217.859375" y="-27" x="-108.9296875" style="fill:#fff8e1 !important;stroke:#f57f17 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-78.9296875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="157.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📚 Repository Pattern</p></span></div></foreignObject></g></g><g transform="translate(2035.640625, 1439)" id="flowchart-ORM-683" class="node default businessClass"><rect height="54" width="172.5" y="-27" x="-86.25" style="fill:#fff8e1 !important;stroke:#f57f17 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-56.25, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🗃️ Django ORM</p></span></div></foreignObject></g></g><g transform="translate(2257.4921875, 1439)" id="flowchart-Cache-684" class="node default businessClass"><rect height="54" width="171.203125" y="-27" x="-85.6015625" style="fill:#fff8e1 !important;stroke:#f57f17 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-55.6015625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="111.203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>⚡ Redis Cache</p></span></div></foreignObject></g></g><g transform="translate(2035.640625, 1617.3154907226562)" id="flowchart-PostgreSQL-685" class="node default dataClass"><path transform="translate(-70.03125, -51.31549162933271)" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container" d="M0,13.210327752888471 a70.03125,13.210327752888471 0,0,0 140.0625,0 a70.03125,13.210327752888471 0,0,0 -140.0625,0 l0,76.21032775288847 a70.03125,13.210327752888471 0,0,0 140.0625,0 l0,-76.21032775288847"></path><g transform="translate(-62.53125, -14)" style="" class="label"><rect></rect><foreignObject height="48" width="125.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🗄️ PostgreSQL<br>Primary Database</p></span></div></foreignObject></g></g><g transform="translate(2257.4921875, 1617.3154907226562)" id="flowchart-Redis-686" class="node default dataClass"><path transform="translate(-68.5234375, -51.11197901138871)" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container" d="M0,13.07465267425914 a68.5234375,13.07465267425914 0,0,0 137.046875,0 a68.5234375,13.07465267425914 0,0,0 -137.046875,0 l0,76.07465267425914 a68.5234375,13.07465267425914 0,0,0 137.046875,0 l0,-76.07465267425914"></path><g transform="translate(-61.0234375, -14)" style="" class="label"><rect></rect><foreignObject height="48" width="122.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔴 Redis<br>Cache &amp; Sessions</p></span></div></foreignObject></g></g><g transform="translate(2426.1328125, 1617.3154907226562)" id="flowchart-S3-687" class="node default dataClass"><path transform="translate(-50.1171875, -48.18834547346514)" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container" d="M0,11.12556364897676 a50.1171875,11.12556364897676 0,0,0 100.234375,0 a50.1171875,11.12556364897676 0,0,0 -100.234375,0 l0,74.12556364897677 a50.1171875,11.12556364897676 0,0,0 100.234375,0 l0,-74.12556364897677"></path><g transform="translate(-42.6171875, -14)" style="" class="label"><rect></rect><foreignObject height="48" width="85.234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📦 AWS S3<br>File Storage</p></span></div></foreignObject></g></g><g transform="translate(148.3671875, 1310)" id="flowchart-YemenMobileAPI-688" class="node default externalClass"><rect height="54" width="210.734375" y="-27" x="-105.3671875" style="fill:#f1f8e9 !important;stroke:#33691e !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-75.3671875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="150.734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📞 Yemen Mobile API</p></span></div></foreignObject></g></g><g transform="translate(375.375, 1310)" id="flowchart-MTNAPI-689" class="node default externalClass"><rect height="54" width="143.28125" y="-27" x="-71.640625" style="fill:#f1f8e9 !important;stroke:#33691e !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-41.640625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="83.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📞 MTN API</p></span></div></foreignObject></g></g><g transform="translate(591.796875, 1310)" id="flowchart-SabaphoneAPI-690" class="node default externalClass"><rect height="54" width="189.5625" y="-27" x="-94.78125" style="fill:#f1f8e9 !important;stroke:#33691e !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-64.78125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="129.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📞 Sabaphone API</p></span></div></foreignObject></g></g><g transform="translate(831.15625, 1310)" id="flowchart-YemenNetAPI-691" class="node default externalClass"><rect height="54" width="189.15625" y="-27" x="-94.578125" style="fill:#f1f8e9 !important;stroke:#33691e !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-64.578125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="129.15625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🌐 Yemen Net API</p></span></div></foreignObject></g></g><g transform="translate(1086.703125, 1807.6309814453125)" id="flowchart-Sentry-692" class="node default monitoringClass"><rect height="78" width="159.828125" y="-39" x="-79.9140625" style="fill:#e0f2f1 !important;stroke:#00695c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-49.9140625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="99.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🐛 Sentry<br>Error Tracking</p></span></div></foreignObject></g></g><g transform="translate(1659.046875, 1807.6309814453125)" id="flowchart-Prometheus-693" class="node default monitoringClass"><rect height="78" width="169.6875" y="-39" x="-84.84375" style="fill:#e0f2f1 !important;stroke:#00695c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-54.84375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="109.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📊 Prometheus<br>Metrics</p></span></div></foreignObject></g></g><g transform="translate(1865.1875, 1807.6309814453125)" id="flowchart-Grafana-694" class="node default monitoringClass"><rect height="78" width="142.59375" y="-39" x="-71.296875" style="fill:#e0f2f1 !important;stroke:#00695c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-41.296875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="82.59375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📈 Grafana<br>Dashboards</p></span></div></foreignObject></g></g><g transform="translate(2075.90234375, 1807.6309814453125)" id="flowchart-ELK-695" class="node default monitoringClass"><rect height="78" width="152.5625" y="-39" x="-76.28125" style="fill:#e0f2f1 !important;stroke:#00695c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-46.28125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="92.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📋 ELK Stack<br>Logging</p></span></div></foreignObject></g></g></g></g></g></svg>