sequenceDiagram
    participant User as 👤 المستخدم
    participant Frontend as 🌐 الواجهة الأمامية
    participant Auth as 🔐 خدمة المصادقة
    participant Wallet as 💰 خدمة المحافظ
    participant Transaction as 💳 خدمة المعاملات
    participant DB as 🗄️ قاعدة البيانات
    participant API as 📞 API خارجية
    participant Notification as 📢 خدمة الإشعارات
    
    User->>Frontend: 1. طلب شحن رصيد
    Frontend->>Auth: 2. التحقق من المصادقة
    Auth-->>Frontend: 3. تأكيد المصادقة
    
    Frontend->>Wallet: 4. التحقق من رصيد المحفظة
    Wallet->>DB: 5. استعلام الرصيد
    DB-->>Wallet: 6. إرجاع الرصيد
    Wallet-->>Frontend: 7. تأكيد توفر الرصيد
    
    Frontend->>Transaction: 8. إنشاء معاملة جديدة
    Transaction->>DB: 9. حفظ المعاملة (حالة: pending)
    DB-->>Transaction: 10. تأكيد الحفظ
    
    Transaction->>Wallet: 11. خصم المبلغ من المحفظة
    Wallet->>DB: 12. تحديث رصيد المحفظة
    DB-->>Wallet: 13. تأكيد التحديث
    
    Transaction->>API: 14. إرسال طلب شحن للـ API الخارجية
    
    alt نجاح العملية
        API-->>Transaction: 15a. استجابة نجاح (resultCode: 0)
        Transaction->>DB: 16a. تحديث حالة المعاملة (success)
        Transaction->>Notification: 17a. إرسال إشعار نجاح
        Notification-->>User: 18a. إشعار SMS/Push
        Transaction-->>Frontend: 19a. تأكيد النجاح
        Frontend-->>User: 20a. عرض رسالة نجاح
    else عملية قيد المعالجة
        API-->>Transaction: 15b. استجابة معالجة (resultCode: -2)
        Transaction->>DB: 16b. تحديث حالة المعاملة (processing)
        Transaction-->>Frontend: 17b. إشعار بالمعالجة
        Frontend-->>User: 18b. عرض حالة المعالجة
        
        Note over Transaction: مهمة خلفية للتحقق الدوري
        loop كل 30 ثانية
            Transaction->>API: 19b. استعلام حالة العملية
            alt اكتملت العملية
                API-->>Transaction: 20b. حالة مكتملة
                Transaction->>DB: 21b. تحديث الحالة النهائية
                Transaction->>Notification: 22b. إرسال إشعار
                Notification-->>User: 23b. إشعار بالنتيجة النهائية
            end
        end
    else فشل العملية
        API-->>Transaction: 15c. استجابة فشل (resultCode: -1)
        Transaction->>DB: 16c. تحديث حالة المعاملة (failed)
        Transaction->>Wallet: 17c. إرجاع المبلغ للمحفظة
        Wallet->>DB: 18c. تحديث رصيد المحفظة
        Transaction->>Notification: 19c. إرسال إشعار فشل
        Notification-->>User: 20c. إشعار SMS/Push
        Transaction-->>Frontend: 21c. إشعار بالفشل
        Frontend-->>User: 22c. عرض رسالة خطأ
    end
