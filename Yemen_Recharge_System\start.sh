#!/bin/bash

# تعيين الترميز
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8

echo ""
echo "========================================"
echo "   🇾🇪 منصة تعبئة الرصيد اليمنية"
echo "========================================"
echo ""
echo "🚀 بدء تشغيل التطبيق..."
echo ""

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 غير مثبت. يرجى تثبيت Python 3.8+ أولاً"
    echo "📥 Ubuntu/Debian: sudo apt install python3 python3-pip python3-venv"
    echo "📥 macOS: brew install python3"
    exit 1
fi

# التحقق من وجود pip
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 غير متوفر"
    echo "📥 Ubuntu/Debian: sudo apt install python3-pip"
    exit 1
fi

# إنشاء البيئة الافتراضية إذا لم تكن موجودة
if [ ! -d "venv" ]; then
    echo "📦 إنشاء البيئة الافتراضية..."
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo "❌ فشل في إنشاء البيئة الافتراضية"
        exit 1
    fi
fi

# تفعيل البيئة الافتراضية
echo "🔧 تفعيل البيئة الافتراضية..."
source venv/bin/activate
if [ $? -ne 0 ]; then
    echo "❌ فشل في تفعيل البيئة الافتراضية"
    exit 1
fi

# تثبيت التبعيات
echo "📚 تثبيت التبعيات المطلوبة..."
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "❌ فشل في تثبيت التبعيات"
    exit 1
fi

echo ""
echo "✅ تم إعداد التطبيق بنجاح!"
echo ""
echo "🔐 الحسابات التجريبية:"
echo "   👨‍💼 مدير: admin / admin123"
echo "   👤 عميل: customer1 / 123456"
echo "   🏪 وكيل: agent1 / agent123"
echo ""
echo "🌐 سيتم فتح التطبيق على: http://localhost:5000"
echo ""
echo "🚀 بدء التشغيل..."
echo ""

# تشغيل التطبيق
python run.py
