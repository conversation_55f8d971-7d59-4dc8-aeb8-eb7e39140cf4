# إعداد بيئة التطوير - مشروع تعبئة الرصيد

## 🎯 نظرة عامة

هذا الدليل يوضح كيفية إعداد بيئة التطوير الكاملة للمشروع، بما يشمل جميع الأدوات والتقنيات المطلوبة.

## 🖥️ متطلبات النظام

### الحد الأدنى للمتطلبات:
- **نظام التشغيل**: Windows 10/11, macOS 10.15+, Ubuntu 20.04+
- **الذاكرة**: 8GB RAM (16GB مفضل)
- **التخزين**: 50GB مساحة فارغة
- **المعالج**: Intel i5 أو AMD Ryzen 5 أو أحدث

### المتطلبات المفضلة:
- **الذاكرة**: 16GB+ RAM
- **التخزين**: SSD 100GB+
- **المعالج**: Intel i7 أو AMD Ryzen 7 أو أحدث

## 🐍 إعداد Python والواجهة الخلفية

### 1. تثبيت Python 3.11+
```bash
# Windows (باستخدام Chocolatey)
choco install python --version=3.11.0

# macOS (باستخدام Homebrew)
brew install python@3.11

# Ubuntu/Debian
sudo apt update
sudo apt install python3.11 python3.11-venv python3.11-pip
```

### 2. تثبيت Poetry لإدارة التبعيات
```bash
# تثبيت Poetry
curl -sSL https://install.python-poetry.org | python3 -

# إضافة Poetry للـ PATH
export PATH="$HOME/.local/bin:$PATH"

# التحقق من التثبيت
poetry --version
```

### 3. إنشاء مشروع Django
```bash
# إنشاء مجلد المشروع
mkdir yemen-recharge-backend
cd yemen-recharge-backend

# تهيئة Poetry
poetry init

# إضافة التبعيات الأساسية
poetry add django djangorestframework
poetry add psycopg2-binary redis celery
poetry add django-cors-headers django-extensions
poetry add python-decouple django-environ
poetry add pillow boto3 django-storages

# تبعيات التطوير
poetry add --group dev pytest pytest-django
poetry add --group dev black flake8 isort
poetry add --group dev django-debug-toolbar
poetry add --group dev factory-boy faker

# تفعيل البيئة الافتراضية
poetry shell

# إنشاء مشروع Django
django-admin startproject core .
```

### 4. هيكل مشروع Django
```
yemen-recharge-backend/
├── pyproject.toml
├── manage.py
├── core/
│   ├── __init__.py
│   ├── settings/
│   │   ├── __init__.py
│   │   ├── base.py
│   │   ├── development.py
│   │   ├── production.py
│   │   └── testing.py
│   ├── urls.py
│   ├── wsgi.py
│   └── asgi.py
├── apps/
│   ├── __init__.py
│   ├── authentication/
│   ├── transactions/
│   ├── wallets/
│   ├── agents/
│   └── notifications/
├── requirements/
│   ├── base.txt
│   ├── development.txt
│   └── production.txt
├── static/
├── media/
├── templates/
├── tests/
├── docs/
└── scripts/
```

## 🗄️ إعداد قاعدة البيانات

### 1. تثبيت PostgreSQL
```bash
# Windows
choco install postgresql

# macOS
brew install postgresql
brew services start postgresql

# Ubuntu/Debian
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

### 2. إنشاء قاعدة البيانات
```sql
-- الاتصال بـ PostgreSQL
sudo -u postgres psql

-- إنشاء قاعدة البيانات والمستخدم
CREATE DATABASE recharge_db;
CREATE USER recharge_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE recharge_db TO recharge_user;
ALTER USER recharge_user CREATEDB;

-- الخروج
\q
```

### 3. تثبيت Redis
```bash
# Windows (باستخدام WSL أو Docker)
docker run -d -p 6379:6379 redis:7-alpine

# macOS
brew install redis
brew services start redis

# Ubuntu/Debian
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

## 🌐 إعداد Node.js والواجهة الأمامية

### 1. تثبيت Node.js 18+
```bash
# Windows
choco install nodejs

# macOS
brew install node

# Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### 2. إنشاء مشروع Next.js
```bash
# إنشاء مشروع Next.js
npx create-next-app@latest yemen-recharge-frontend --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"

cd yemen-recharge-frontend

# إضافة التبعيات المطلوبة
npm install @tanstack/react-query zustand
npm install @headlessui/react @heroicons/react
npm install react-hook-form @hookform/resolvers yup
npm install axios date-fns clsx
npm install next-i18next react-i18next

# تبعيات التطوير
npm install --save-dev @types/node @types/react @types/react-dom
npm install --save-dev prettier eslint-config-prettier
npm install --save-dev @testing-library/react @testing-library/jest-dom jest jest-environment-jsdom
```

### 3. هيكل مشروع Next.js
```
yemen-recharge-frontend/
├── package.json
├── next.config.js
├── tailwind.config.js
├── tsconfig.json
├── src/
│   ├── app/
│   │   ├── layout.tsx
│   │   ├── page.tsx
│   │   ├── (auth)/
│   │   ├── dashboard/
│   │   ├── recharge/
│   │   └── transactions/
│   ├── components/
│   │   ├── ui/
│   │   ├── forms/
│   │   ├── layouts/
│   │   └── features/
│   ├── hooks/
│   ├── stores/
│   ├── utils/
│   ├── types/
│   └── styles/
├── public/
├── locales/
└── __tests__/
```

## 📱 إعداد React Native

### 1. تثبيت React Native CLI
```bash
# تثبيت React Native CLI
npm install -g @react-native-community/cli

# تثبيت Expo CLI (اختياري)
npm install -g @expo/cli
```

### 2. إعداد بيئة Android
```bash
# تحميل وتثبيت Android Studio
# إعداد Android SDK
# إضافة متغيرات البيئة
export ANDROID_HOME=$HOME/Android/Sdk
export PATH=$PATH:$ANDROID_HOME/emulator
export PATH=$PATH:$ANDROID_HOME/tools
export PATH=$PATH:$ANDROID_HOME/tools/bin
export PATH=$PATH:$ANDROID_HOME/platform-tools
```

### 3. إنشاء مشروع React Native
```bash
# إنشاء مشروع جديد
npx react-native init YemenRechargeApp --template react-native-template-typescript

cd YemenRechargeApp

# إضافة التبعيات المطلوبة
npm install @react-navigation/native @react-navigation/bottom-tabs @react-navigation/stack
npm install react-native-screens react-native-safe-area-context
npm install @react-native-async-storage/async-storage
npm install react-native-vector-icons
npm install react-query axios
npm install react-hook-form
npm install react-native-keychain
```

## 🔧 أدوات التطوير

### 1. VS Code مع الإضافات المطلوبة
```json
// .vscode/extensions.json
{
  "recommendations": [
    "ms-python.python",
    "ms-python.black-formatter",
    "ms-python.flake8",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "ms-vscode-remote.remote-containers",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-python.isort"
  ]
}
```

### 2. إعدادات VS Code
```json
// .vscode/settings.json
{
  "python.defaultInterpreterPath": ".venv/bin/python",
  "python.formatting.provider": "black",
  "python.linting.enabled": true,
  "python.linting.flake8Enabled": true,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true
  },
  "[python]": {
    "editor.defaultFormatter": "ms-python.black-formatter"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  }
}
```

## 🐳 إعداد Docker (اختياري)

### 1. تثبيت Docker
```bash
# Windows/macOS: تحميل Docker Desktop
# Ubuntu/Debian
sudo apt update
sudo apt install docker.io docker-compose
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker $USER
```

### 2. ملفات Docker للمشروع
```dockerfile
# Dockerfile للـ Backend
FROM python:3.11-slim

WORKDIR /app

COPY pyproject.toml poetry.lock ./
RUN pip install poetry && poetry install --no-dev

COPY . .

EXPOSE 8000

CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]
```

```dockerfile
# Dockerfile للـ Frontend
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: recharge_db
      POSTGRES_USER: recharge_user
      POSTGRES_PASSWORD: secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  backend:
    build: ./yemen-recharge-backend
    ports:
      - "8000:8000"
    depends_on:
      - db
      - redis
    environment:
      - DATABASE_URL=**************************************************/recharge_db
      - REDIS_URL=redis://redis:6379/0

  frontend:
    build: ./yemen-recharge-frontend
    ports:
      - "3000:3000"
    depends_on:
      - backend

volumes:
  postgres_data:
```

## 🔧 أدوات إضافية

### 1. Git وإدارة الإصدارات
```bash
# تهيئة Git
git init
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# إنشاء .gitignore
echo "node_modules/
.env
.venv/
__pycache__/
*.pyc
.DS_Store
.vscode/settings.json" > .gitignore
```

### 2. أدوات التحليل والجودة
```bash
# تثبيت أدوات Python
poetry add --group dev pre-commit
poetry add --group dev bandit safety

# تثبيت أدوات JavaScript
npm install --save-dev husky lint-staged
npm install --save-dev @typescript-eslint/parser @typescript-eslint/eslint-plugin
```

### 3. إعداد Pre-commit Hooks
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files

  - repo: https://github.com/psf/black
    rev: 22.10.0
    hooks:
      - id: black

  - repo: https://github.com/pycqa/flake8
    rev: 5.0.4
    hooks:
      - id: flake8

  - repo: https://github.com/pycqa/isort
    rev: 5.10.1
    hooks:
      - id: isort
```

## ⚙️ ملفات التكوين

### 1. إعدادات Django
```python
# core/settings/base.py
import os
from pathlib import Path
from decouple import config

BASE_DIR = Path(__file__).resolve().parent.parent.parent

SECRET_KEY = config('SECRET_KEY')
DEBUG = config('DEBUG', default=False, cast=bool)

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    # Third party apps
    'rest_framework',
    'corsheaders',
    'django_extensions',

    # Local apps
    'apps.authentication',
    'apps.transactions',
    'apps.wallets',
    'apps.agents',
    'apps.notifications',
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'core.urls'

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': config('DB_NAME'),
        'USER': config('DB_USER'),
        'PASSWORD': config('DB_PASSWORD'),
        'HOST': config('DB_HOST', default='localhost'),
        'PORT': config('DB_PORT', default='5432'),
    }
}

# Redis Configuration
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': config('REDIS_URL', default='redis://127.0.0.1:6379/1'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# Celery Configuration
CELERY_BROKER_URL = config('CELERY_BROKER_URL', default='redis://127.0.0.1:6379/0')
CELERY_RESULT_BACKEND = config('CELERY_RESULT_BACKEND', default='redis://127.0.0.1:6379/0')
```

### 2. ملف البيئة (.env)
```bash
# .env
SECRET_KEY=your-secret-key-here
DEBUG=True

# Database
DB_NAME=recharge_db
DB_USER=recharge_user
DB_PASSWORD=secure_password
DB_HOST=localhost
DB_PORT=5432

# Redis
REDIS_URL=redis://127.0.0.1:6379/1

# Celery
CELERY_BROKER_URL=redis://127.0.0.1:6379/0
CELERY_RESULT_BACKEND=redis://127.0.0.1:6379/0

# External APIs
YEMEN_MOBILE_API_URL=https://api.yemenmobile.com
YEMEN_MOBILE_API_KEY=your-api-key
MTN_API_URL=https://api.mtn.ye
MTN_API_KEY=your-api-key

# Email Settings
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# SMS Settings
SMS_PROVIDER=twilio
TWILIO_ACCOUNT_SID=your-account-sid
TWILIO_AUTH_TOKEN=your-auth-token
```

### 3. إعدادات Next.js
```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
    NEXT_PUBLIC_APP_NAME: process.env.NEXT_PUBLIC_APP_NAME,
  },
  i18n: {
    locales: ['ar', 'en'],
    defaultLocale: 'ar',
    localeDetection: false,
  },
  images: {
    domains: ['localhost', 'your-domain.com'],
  },
}

module.exports = nextConfig
```

### 4. إعدادات TypeScript
```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/utils/*": ["./src/utils/*"],
      "@/types/*": ["./src/types/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

## 🧪 إعداد البيئة للاختبار

### 1. إعدادات pytest للـ Backend
```python
# pytest.ini
[tool:pytest]
DJANGO_SETTINGS_MODULE = core.settings.testing
python_files = tests.py test_*.py *_tests.py
addopts = --tb=short --strict-markers --disable-warnings
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
```

### 2. إعدادات Jest للـ Frontend
```javascript
// jest.config.js
const nextJest = require('next/jest')

const createJestConfig = nextJest({
  dir: './',
})

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapping: {
    '^@/components/(.*)$': '<rootDir>/src/components/$1',
    '^@/utils/(.*)$': '<rootDir>/src/utils/$1',
    '^@/types/(.*)$': '<rootDir>/src/types/$1',
  },
  testEnvironment: 'jest-environment-jsdom',
}

module.exports = createJestConfig(customJestConfig)
```

## 📝 سكريبت الإعداد التلقائي

### 1. سكريبت إعداد شامل (setup.sh)
```bash
#!/bin/bash

echo "🚀 بدء إعداد بيئة التطوير..."

# التحقق من متطلبات النظام
echo "📋 التحقق من متطلبات النظام..."

# التحقق من Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 غير مثبت. يرجى تثبيته أولاً."
    exit 1
fi

# التحقق من Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js غير مثبت. يرجى تثبيته أولاً."
    exit 1
fi

# التحقق من PostgreSQL
if ! command -v psql &> /dev/null; then
    echo "❌ PostgreSQL غير مثبت. يرجى تثبيته أولاً."
    exit 1
fi

echo "✅ جميع المتطلبات متوفرة"

# إعداد Backend
echo "🐍 إعداد Backend..."
cd backend
poetry install
poetry run python manage.py migrate
poetry run python manage.py collectstatic --noinput
cd ..

# إعداد Frontend
echo "🌐 إعداد Frontend..."
cd frontend
npm install
npm run build
cd ..

# إعداد Mobile App
echo "📱 إعداد Mobile App..."
cd mobile
npm install
cd ..

echo "🎉 تم إعداد بيئة التطوير بنجاح!"
echo "📖 راجع README.md للحصول على تعليمات التشغيل"
```

### 2. سكريبت تشغيل التطوير (dev.sh)
```bash
#!/bin/bash

echo "🚀 بدء خوادم التطوير..."

# تشغيل PostgreSQL و Redis
echo "🗄️ بدء قواعد البيانات..."
docker-compose up -d db redis

# تشغيل Backend
echo "🐍 بدء Backend..."
cd backend
poetry run python manage.py runserver &
BACKEND_PID=$!

# تشغيل Celery
poetry run celery -A core worker -l info &
CELERY_PID=$!

cd ..

# تشغيل Frontend
echo "🌐 بدء Frontend..."
cd frontend
npm run dev &
FRONTEND_PID=$!
cd ..

echo "✅ جميع الخوادم تعمل:"
echo "   Backend: http://localhost:8000"
echo "   Frontend: http://localhost:3000"
echo "   Admin: http://localhost:8000/admin"

# انتظار إيقاف العمليات
trap "kill $BACKEND_PID $CELERY_PID $FRONTEND_PID; docker-compose down" EXIT
wait
```

## 📚 الوثائق والمراجع

### 1. ملف README.md
```markdown
# Yemen Recharge Platform

## 🚀 البدء السريع

### المتطلبات
- Python 3.11+
- Node.js 18+
- PostgreSQL 15+
- Redis 7+

### التثبيت
```bash
# استنساخ المشروع
git clone https://github.com/your-org/yemen-recharge.git
cd yemen-recharge

# تشغيل سكريبت الإعداد
chmod +x setup.sh
./setup.sh

# تشغيل خوادم التطوير
chmod +x dev.sh
./dev.sh
```

### الاختبار
```bash
# اختبار Backend
cd backend
poetry run pytest

# اختبار Frontend
cd frontend
npm test
```

### النشر
راجع دليل النشر في `docs/deployment.md`
```

### 2. دليل المساهمة (CONTRIBUTING.md)
```markdown
# دليل المساهمة

## معايير الكود
- استخدم Black لتنسيق Python
- استخدم Prettier لتنسيق JavaScript/TypeScript
- اكتب اختبارات لجميع المميزات الجديدة
- اتبع معايير التسمية المتفق عليها

## عملية المراجعة
1. أنشئ فرع جديد للميزة
2. اكتب الكود والاختبارات
3. تأكد من نجاح جميع الاختبارات
4. أنشئ Pull Request
5. انتظر المراجعة والموافقة
```

هذا الدليل الشامل يوفر كل ما يحتاجه المطور لإعداد بيئة تطوير كاملة وفعالة للمشروع.
