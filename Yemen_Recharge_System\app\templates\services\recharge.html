{% extends "base.html" %}

{% block title %}شحن رصيد - منصة تعبئة الرصيد اليمنية{% endblock %}

{% block content %}
<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-custom">
    <div class="container-fluid">
        <a class="navbar-brand" href="{{ url_for('dashboard') }}">
            <i class="fas fa-mobile-alt me-2"></i>
            منصة تعبئة الرصيد
        </a>
        
        <div class="navbar-nav me-auto">
            <a class="nav-link" href="{{ url_for('dashboard') }}">
                <i class="fas fa-arrow-right me-1"></i>العودة للرئيسية
            </a>
        </div>
        
        <div class="navbar-nav">
            <span class="nav-link text-white">
                <i class="fas fa-wallet me-1"></i>
                الرصيد: <span id="current-balance">{{ current_user.balance|number_format }}</span> ريال
            </span>
        </div>
    </div>
</nav>

<div class="container-fluid">
    <div class="main-container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="p-4">
                    <!-- Header -->
                    <div class="text-center mb-5">
                        <div class="mb-3">
                            <i class="fas fa-mobile-alt" style="font-size: 4rem; color: var(--primary-color);"></i>
                        </div>
                        <h2 class="fw-bold text-dark mb-2">شحن رصيد الهاتف</h2>
                        <p class="text-muted">اختر الشركة وأدخل الرقم والمبلغ لشحن الرصيد فوراً</p>
                    </div>

                    <!-- Recharge Form -->
                    <div class="card-custom">
                        <div class="card-header-custom">
                            <h5 class="mb-0">
                                <i class="fas fa-edit me-2"></i>
                                بيانات الشحن
                            </h5>
                        </div>
                        <div class="card-body p-4">
                            <form id="rechargeForm">
                                <!-- Provider Selection -->
                                <div class="mb-4">
                                    <label class="form-label fw-semibold mb-3">
                                        <i class="fas fa-building me-2"></i>اختر شركة الاتصالات
                                    </label>
                                    <div class="row g-3">
                                        <div class="col-md-3 col-6">
                                            <input type="radio" class="btn-check" name="provider" id="yemen_mobile" value="يمن موبايل" checked>
                                            <label class="btn btn-outline-primary-custom w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3" for="yemen_mobile">
                                                <i class="fas fa-mobile-alt mb-2" style="font-size: 2rem;"></i>
                                                <span class="fw-bold">يمن موبايل</span>
                                            </label>
                                        </div>
                                        <div class="col-md-3 col-6">
                                            <input type="radio" class="btn-check" name="provider" id="mtn" value="MTN">
                                            <label class="btn btn-outline-primary-custom w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3" for="mtn">
                                                <i class="fas fa-signal mb-2" style="font-size: 2rem;"></i>
                                                <span class="fw-bold">MTN</span>
                                            </label>
                                        </div>
                                        <div class="col-md-3 col-6">
                                            <input type="radio" class="btn-check" name="provider" id="sabaphone" value="سبأفون">
                                            <label class="btn btn-outline-primary-custom w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3" for="sabaphone">
                                                <i class="fas fa-phone mb-2" style="font-size: 2rem;"></i>
                                                <span class="fw-bold">سبأفون</span>
                                            </label>
                                        </div>
                                        <div class="col-md-3 col-6">
                                            <input type="radio" class="btn-check" name="provider" id="y" value="واي">
                                            <label class="btn btn-outline-primary-custom w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3" for="y">
                                                <i class="fas fa-wifi mb-2" style="font-size: 2rem;"></i>
                                                <span class="fw-bold">واي</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Phone Number -->
                                <div class="mb-4">
                                    <label for="phone_number" class="form-label fw-semibold">
                                        <i class="fas fa-phone me-2"></i>رقم الهاتف
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">+967</span>
                                        <input type="tel" 
                                               class="form-control form-control-custom" 
                                               id="phone_number" 
                                               name="phone_number" 
                                               placeholder="7XXXXXXXX"
                                               pattern="[7][0-9]{8}"
                                               maxlength="9"
                                               required>
                                    </div>
                                    <div class="form-text">أدخل رقم الهاتف بدون رمز البلد (9 أرقام)</div>
                                </div>

                                <!-- Amount Selection -->
                                <div class="mb-4">
                                    <label class="form-label fw-semibold mb-3">
                                        <i class="fas fa-money-bill-wave me-2"></i>اختر مبلغ الشحن
                                    </label>
                                    <div class="row g-3">
                                        <div class="col-md-3 col-6">
                                            <input type="radio" class="btn-check" name="amount" id="amount_500" value="500">
                                            <label class="btn btn-outline-success w-100 p-3 text-center" for="amount_500">
                                                <div class="fw-bold">500</div>
                                                <small>ريال</small>
                                            </label>
                                        </div>
                                        <div class="col-md-3 col-6">
                                            <input type="radio" class="btn-check" name="amount" id="amount_1000" value="1000" checked>
                                            <label class="btn btn-outline-success w-100 p-3 text-center" for="amount_1000">
                                                <div class="fw-bold">1,000</div>
                                                <small>ريال</small>
                                            </label>
                                        </div>
                                        <div class="col-md-3 col-6">
                                            <input type="radio" class="btn-check" name="amount" id="amount_2000" value="2000">
                                            <label class="btn btn-outline-success w-100 p-3 text-center" for="amount_2000">
                                                <div class="fw-bold">2,000</div>
                                                <small>ريال</small>
                                            </label>
                                        </div>
                                        <div class="col-md-3 col-6">
                                            <input type="radio" class="btn-check" name="amount" id="amount_5000" value="5000">
                                            <label class="btn btn-outline-success w-100 p-3 text-center" for="amount_5000">
                                                <div class="fw-bold">5,000</div>
                                                <small>ريال</small>
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <!-- Custom Amount -->
                                    <div class="mt-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="amount" id="custom_amount" value="custom">
                                            <label class="form-check-label fw-semibold" for="custom_amount">
                                                مبلغ آخر
                                            </label>
                                        </div>
                                        <div class="mt-2" id="customAmountDiv" style="display: none;">
                                            <input type="number" 
                                                   class="form-control form-control-custom" 
                                                   id="custom_amount_value" 
                                                   placeholder="أدخل المبلغ"
                                                   min="100"
                                                   max="50000">
                                        </div>
                                    </div>
                                </div>

                                <!-- Summary -->
                                <div class="mb-4">
                                    <div class="card" style="background: #f8f9fa; border: 2px dashed var(--primary-color);">
                                        <div class="card-body">
                                            <h6 class="fw-bold mb-3">
                                                <i class="fas fa-receipt me-2"></i>ملخص العملية
                                            </h6>
                                            <div class="row">
                                                <div class="col-6">
                                                    <small class="text-muted">الشركة:</small>
                                                    <div class="fw-bold" id="summary_provider">يمن موبايل</div>
                                                </div>
                                                <div class="col-6">
                                                    <small class="text-muted">الرقم:</small>
                                                    <div class="fw-bold" id="summary_phone">-</div>
                                                </div>
                                                <div class="col-6 mt-2">
                                                    <small class="text-muted">المبلغ:</small>
                                                    <div class="fw-bold text-success" id="summary_amount">1,000 ريال</div>
                                                </div>
                                                <div class="col-6 mt-2">
                                                    <small class="text-muted">الرسوم:</small>
                                                    <div class="fw-bold" id="summary_fees">10 ريال</div>
                                                </div>
                                                <div class="col-12 mt-3 pt-3 border-top">
                                                    <div class="d-flex justify-content-between">
                                                        <span class="fw-bold">الإجمالي:</span>
                                                        <span class="fw-bold text-primary fs-5" id="summary_total">1,010 ريال</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Submit Button -->
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary-custom btn-lg" id="submitBtn">
                                        <span id="submitText">
                                            <i class="fas fa-credit-card me-2"></i>
                                            تأكيد الشحن
                                        </span>
                                        <span id="submitSpinner" class="d-none">
                                            <span class="loading me-2"></span>
                                            جاري المعالجة...
                                        </span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Recent Numbers -->
                    <div class="card-custom mt-4">
                        <div class="card-header-custom">
                            <h6 class="mb-0">
                                <i class="fas fa-history me-2"></i>
                                الأرقام المستخدمة مؤخراً
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-2">
                                <div class="col-md-4">
                                    <button class="btn btn-outline-secondary btn-sm w-100" onclick="fillPhone('771234567')">
                                        <i class="fas fa-phone me-1"></i>771234567
                                    </button>
                                </div>
                                <div class="col-md-4">
                                    <button class="btn btn-outline-secondary btn-sm w-100" onclick="fillPhone('733456789')">
                                        <i class="fas fa-phone me-1"></i>733456789
                                    </button>
                                </div>
                                <div class="col-md-4">
                                    <button class="btn btn-outline-secondary btn-sm w-100" onclick="fillPhone('777123456')">
                                        <i class="fas fa-phone me-1"></i>777123456
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div class="modal fade" id="successModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center p-5">
                <div class="mb-4">
                    <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                </div>
                <h4 class="fw-bold text-success mb-3">تم الشحن بنجاح!</h4>
                <p class="text-muted mb-4">تم شحن رصيد الرقم <span id="success_phone" class="fw-bold"></span> بمبلغ <span id="success_amount" class="fw-bold text-success"></span></p>
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-primary-custom" onclick="location.reload()">
                        شحن رقم آخر
                    </button>
                    <button type="button" class="btn btn-outline-primary-custom" onclick="location.href='{{ url_for('dashboard') }}'">
                        العودة للرئيسية
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحديث الملخص عند تغيير البيانات
    document.addEventListener('DOMContentLoaded', function() {
        updateSummary();
        
        // مراقبة تغيير الشركة
        document.querySelectorAll('input[name="provider"]').forEach(radio => {
            radio.addEventListener('change', updateSummary);
        });
        
        // مراقبة تغيير المبلغ
        document.querySelectorAll('input[name="amount"]').forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.value === 'custom') {
                    document.getElementById('customAmountDiv').style.display = 'block';
                    document.getElementById('custom_amount_value').focus();
                } else {
                    document.getElementById('customAmountDiv').style.display = 'none';
                }
                updateSummary();
            });
        });
        
        // مراقبة تغيير رقم الهاتف
        document.getElementById('phone_number').addEventListener('input', updateSummary);
        
        // مراقبة تغيير المبلغ المخصص
        document.getElementById('custom_amount_value').addEventListener('input', updateSummary);
    });

    function updateSummary() {
        const provider = document.querySelector('input[name="provider"]:checked')?.value || '';
        const phone = document.getElementById('phone_number').value;
        const amountRadio = document.querySelector('input[name="amount"]:checked');
        
        let amount = 0;
        if (amountRadio) {
            if (amountRadio.value === 'custom') {
                amount = parseInt(document.getElementById('custom_amount_value').value) || 0;
            } else {
                amount = parseInt(amountRadio.value);
            }
        }
        
        const fees = Math.max(10, amount * 0.01); // رسوم 1% أو 10 ريال كحد أدنى
        const total = amount + fees;
        
        document.getElementById('summary_provider').textContent = provider;
        document.getElementById('summary_phone').textContent = phone ? `+967${phone}` : '-';
        document.getElementById('summary_amount').textContent = amount ? `${amount.toLocaleString('ar-YE')} ريال` : '-';
        document.getElementById('summary_fees').textContent = amount ? `${Math.round(fees).toLocaleString('ar-YE')} ريال` : '-';
        document.getElementById('summary_total').textContent = amount ? `${Math.round(total).toLocaleString('ar-YE')} ريال` : '-';
    }

    function fillPhone(phoneNumber) {
        document.getElementById('phone_number').value = phoneNumber;
        updateSummary();
        
        // تأثير بصري
        const phoneField = document.getElementById('phone_number');
        phoneField.style.background = '#e8f5e8';
        setTimeout(() => {
            phoneField.style.background = '';
        }, 1000);
    }

    // معالجة إرسال النموذج
    document.getElementById('rechargeForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const submitBtn = document.getElementById('submitBtn');
        const submitText = document.getElementById('submitText');
        const submitSpinner = document.getElementById('submitSpinner');
        
        // التحقق من البيانات
        const provider = document.querySelector('input[name="provider"]:checked')?.value;
        const phone = document.getElementById('phone_number').value;
        const amountRadio = document.querySelector('input[name="amount"]:checked');
        
        if (!provider || !phone || !amountRadio) {
            showAlert('يرجى إكمال جميع البيانات المطلوبة', 'error');
            return;
        }
        
        let amount = 0;
        if (amountRadio.value === 'custom') {
            amount = parseInt(document.getElementById('custom_amount_value').value);
            if (!amount || amount < 100 || amount > 50000) {
                showAlert('يرجى إدخال مبلغ صحيح (100 - 50,000 ريال)', 'error');
                return;
            }
        } else {
            amount = parseInt(amountRadio.value);
        }
        
        // التحقق من الرصيد
        const currentBalance = {{ current_user.balance }};
        const total = amount + Math.max(10, amount * 0.01);
        
        if (total > currentBalance) {
            showAlert('رصيدك غير كافي لإتمام هذه العملية', 'error');
            return;
        }
        
        // إظهار مؤشر التحميل
        submitText.classList.add('d-none');
        submitSpinner.classList.remove('d-none');
        submitBtn.disabled = true;
        
        // إرسال الطلب
        const formData = {
            provider: provider,
            phone_number: phone,
            amount: amount
        };
        
        fetch('/api/process_recharge', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // إظهار نافذة النجاح
                document.getElementById('success_phone').textContent = `+967${phone}`;
                document.getElementById('success_amount').textContent = `${amount.toLocaleString('ar-YE')} ريال`;
                
                const successModal = new bootstrap.Modal(document.getElementById('successModal'));
                successModal.show();
                
                // تحديث الرصيد
                updateBalance();
            } else {
                showAlert(data.message || 'حدث خطأ أثناء معالجة الطلب', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى', 'error');
        })
        .finally(() => {
            // إخفاء مؤشر التحميل
            submitText.classList.remove('d-none');
            submitSpinner.classList.add('d-none');
            submitBtn.disabled = false;
        });
    });

    // تنسيق رقم الهاتف أثناء الكتابة
    document.getElementById('phone_number').addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, ''); // إزالة كل شيء عدا الأرقام
        if (value.length > 9) {
            value = value.substring(0, 9);
        }
        e.target.value = value;
        
        // التحقق من صحة الرقم
        if (value.length === 9 && value.startsWith('7')) {
            e.target.style.borderColor = 'var(--success-color)';
        } else if (value.length > 0) {
            e.target.style.borderColor = 'var(--error-color)';
        } else {
            e.target.style.borderColor = 'var(--gray-300)';
        }
    });
</script>
{% endblock %}
