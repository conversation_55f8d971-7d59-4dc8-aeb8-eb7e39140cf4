{% extends "base.html" %}

{% block title %}لوحة التحكم - منصة تعبئة الرصيد اليمنية{% endblock %}

{% block content %}
<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-custom">
    <div class="container-fluid">
        <a class="navbar-brand" href="{{ url_for('dashboard') }}">
            <i class="fas fa-mobile-alt me-2"></i>
            منصة تعبئة الرصيد
        </a>
        
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link active" href="{{ url_for('dashboard') }}">
                        <i class="fas fa-home me-1"></i>الرئيسية
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('recharge') }}">
                        <i class="fas fa-mobile-alt me-1"></i>شحن رصيد
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('packages') }}">
                        <i class="fas fa-box me-1"></i>الباقات
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('bills') }}">
                        <i class="fas fa-file-invoice me-1"></i>الفواتير
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('transfer') }}">
                        <i class="fas fa-exchange-alt me-1"></i>التحويلات
                    </a>
                </li>
            </ul>
            
            <ul class="navbar-nav">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        {{ current_user.full_name }}
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="{{ url_for('profile') }}">
                            <i class="fas fa-user me-2"></i>الملف الشخصي
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('wallet') }}">
                            <i class="fas fa-wallet me-2"></i>المحفظة
                        </a></li>
                        {% if current_user.user_type == 'admin' %}
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('admin_dashboard') }}">
                            <i class="fas fa-cog me-2"></i>لوحة الإدارة
                        </a></li>
                        {% endif %}
                        {% if current_user.user_type in ['agent', 'admin'] %}
                        <li><a class="dropdown-item" href="{{ url_for('agent_dashboard') }}">
                            <i class="fas fa-store me-2"></i>لوحة الوكيل
                        </a></li>
                        {% endif %}
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<div class="container-fluid">
    <div class="main-container">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'error' if category == 'error' else category }}-custom alert-dismissible fade show m-4" role="alert">
                        <i class="fas fa-{{ 'exclamation-circle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-9 col-md-8">
                <div class="p-4">
                    <!-- Welcome Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h2 class="fw-bold text-dark mb-1">مرحباً، {{ current_user.full_name }}</h2>
                                    <p class="text-muted mb-0">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ moment().format('dddd، DD MMMM YYYY') }}
                                    </p>
                                </div>
                                <div class="text-end">
                                    <div class="badge bg-success fs-6 p-3 rounded-pill">
                                        <i class="fas fa-wallet me-2"></i>
                                        رصيدك: <span id="user-balance">{{ current_user.balance|number_format }}</span> ريال
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card">
                                <div class="stats-icon primary">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="stats-number">{{ stats.total_transactions }}</div>
                                <div class="stats-label">إجمالي المعاملات</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card">
                                <div class="stats-icon success">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="stats-number">{{ stats.successful_transactions }}</div>
                                <div class="stats-label">معاملات ناجحة</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card">
                                <div class="stats-icon warning">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="stats-number">{{ stats.pending_transactions }}</div>
                                <div class="stats-label">قيد المعالجة</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="stats-card">
                                <div class="stats-icon secondary">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div class="stats-number">{{ stats.total_spent|number_format }}</div>
                                <div class="stats-label">إجمالي الإنفاق</div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Services -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card-custom">
                                <div class="card-header-custom">
                                    <h5 class="mb-0">
                                        <i class="fas fa-rocket me-2"></i>
                                        الخدمات السريعة
                                    </h5>
                                </div>
                                <div class="card-body p-4">
                                    <div class="row g-3">
                                        <div class="col-lg-3 col-md-6">
                                            <a href="{{ url_for('recharge') }}" class="text-decoration-none">
                                                <div class="service-card text-center p-4 h-100">
                                                    <i class="fas fa-mobile-alt text-primary mb-3" style="font-size: 2.5rem;"></i>
                                                    <h6 class="fw-bold">شحن رصيد</h6>
                                                    <p class="text-muted small mb-0">شحن رصيد الهاتف فوراً</p>
                                                </div>
                                            </a>
                                        </div>
                                        <div class="col-lg-3 col-md-6">
                                            <a href="{{ url_for('packages') }}" class="text-decoration-none">
                                                <div class="service-card text-center p-4 h-100">
                                                    <i class="fas fa-box text-success mb-3" style="font-size: 2.5rem;"></i>
                                                    <h6 class="fw-bold">الباقات</h6>
                                                    <p class="text-muted small mb-0">باقات الإنترنت والمكالمات</p>
                                                </div>
                                            </a>
                                        </div>
                                        <div class="col-lg-3 col-md-6">
                                            <a href="{{ url_for('bills') }}" class="text-decoration-none">
                                                <div class="service-card text-center p-4 h-100">
                                                    <i class="fas fa-file-invoice text-warning mb-3" style="font-size: 2.5rem;"></i>
                                                    <h6 class="fw-bold">دفع الفواتير</h6>
                                                    <p class="text-muted small mb-0">فواتير الكهرباء والماء</p>
                                                </div>
                                            </a>
                                        </div>
                                        <div class="col-lg-3 col-md-6">
                                            <a href="{{ url_for('transfer') }}" class="text-decoration-none">
                                                <div class="service-card text-center p-4 h-100">
                                                    <i class="fas fa-exchange-alt text-info mb-3" style="font-size: 2.5rem;"></i>
                                                    <h6 class="fw-bold">تحويل أموال</h6>
                                                    <p class="text-muted small mb-0">تحويل للأصدقاء والعائلة</p>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Transactions -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card-custom">
                                <div class="card-header-custom d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">
                                        <i class="fas fa-history me-2"></i>
                                        آخر المعاملات
                                    </h5>
                                    <a href="{{ url_for('wallet') }}" class="btn btn-outline-light btn-sm">
                                        عرض الكل
                                    </a>
                                </div>
                                <div class="card-body p-0">
                                    {% if transactions %}
                                        {% for transaction in transactions %}
                                        <div class="transaction-item border-bottom">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <div class="me-3">
                                                        {% if transaction.type == 'recharge' %}
                                                            <i class="fas fa-mobile-alt text-primary"></i>
                                                        {% elif transaction.type == 'package' %}
                                                            <i class="fas fa-box text-success"></i>
                                                        {% elif transaction.type == 'bill' %}
                                                            <i class="fas fa-file-invoice text-warning"></i>
                                                        {% else %}
                                                            <i class="fas fa-exchange-alt text-info"></i>
                                                        {% endif %}
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-1">{{ transaction.description }}</h6>
                                                        <small class="text-muted">
                                                            {{ transaction.provider }} - {{ transaction.target_number }}
                                                        </small>
                                                        <br>
                                                        <small class="text-muted">
                                                            <i class="fas fa-clock me-1"></i>
                                                            {{ transaction.created_at.strftime('%Y-%m-%d %H:%M') }}
                                                        </small>
                                                    </div>
                                                </div>
                                                <div class="text-end">
                                                    <div class="fw-bold text-dark mb-1">
                                                        {{ transaction.amount|number_format }} ريال
                                                    </div>
                                                    <span class="status-badge status-{{ transaction.status }}">
                                                        {% if transaction.status == 'success' %}
                                                            <i class="fas fa-check me-1"></i>نجح
                                                        {% elif transaction.status == 'pending' %}
                                                            <i class="fas fa-clock me-1"></i>معالجة
                                                        {% else %}
                                                            <i class="fas fa-times me-1"></i>فشل
                                                        {% endif %}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    {% else %}
                                        <div class="text-center p-5">
                                            <i class="fas fa-inbox text-muted mb-3" style="font-size: 3rem;"></i>
                                            <h6 class="text-muted">لا توجد معاملات حتى الآن</h6>
                                            <p class="text-muted small">ابدأ بإجراء أول معاملة لك</p>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-3 col-md-4">
                <div class="sidebar">
                    <!-- Quick Balance -->
                    <div class="p-3 mb-3">
                        <div class="card-custom text-center">
                            <div class="card-body">
                                <i class="fas fa-wallet text-primary mb-2" style="font-size: 2rem;"></i>
                                <h6 class="fw-bold">رصيدك الحالي</h6>
                                <h4 class="text-primary fw-bold mb-3">
                                    <span id="sidebar-balance">{{ current_user.balance|number_format }}</span> ريال
                                </h4>
                                <button class="btn btn-primary-custom btn-sm" onclick="addBalance()">
                                    <i class="fas fa-plus me-1"></i>إضافة رصيد
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Navigation Links -->
                    <div class="px-3">
                        <h6 class="text-muted fw-bold mb-3">الخدمات</h6>
                        <a href="{{ url_for('recharge') }}" class="sidebar-item">
                            <i class="fas fa-mobile-alt"></i>شحن رصيد
                        </a>
                        <a href="{{ url_for('packages') }}" class="sidebar-item">
                            <i class="fas fa-box"></i>الباقات
                        </a>
                        <a href="{{ url_for('bills') }}" class="sidebar-item">
                            <i class="fas fa-file-invoice"></i>الفواتير
                        </a>
                        <a href="{{ url_for('transfer') }}" class="sidebar-item">
                            <i class="fas fa-exchange-alt"></i>التحويلات
                        </a>
                        
                        <h6 class="text-muted fw-bold mb-3 mt-4">الحساب</h6>
                        <a href="{{ url_for('wallet') }}" class="sidebar-item">
                            <i class="fas fa-wallet"></i>المحفظة
                        </a>
                        <a href="{{ url_for('profile') }}" class="sidebar-item">
                            <i class="fas fa-user"></i>الملف الشخصي
                        </a>
                        
                        {% if current_user.user_type in ['admin', 'agent'] %}
                        <h6 class="text-muted fw-bold mb-3 mt-4">الإدارة</h6>
                        {% if current_user.user_type == 'admin' %}
                        <a href="{{ url_for('admin_dashboard') }}" class="sidebar-item">
                            <i class="fas fa-cog"></i>لوحة الإدارة
                        </a>
                        {% endif %}
                        <a href="{{ url_for('agent_dashboard') }}" class="sidebar-item">
                            <i class="fas fa-store"></i>لوحة الوكيل
                        </a>
                        {% endif %}
                    </div>

                    <!-- Support -->
                    <div class="p-3 mt-4">
                        <div class="card-custom">
                            <div class="card-body text-center">
                                <i class="fas fa-headset text-info mb-2" style="font-size: 2rem;"></i>
                                <h6 class="fw-bold">تحتاج مساعدة؟</h6>
                                <p class="small text-muted mb-3">فريق الدعم متاح 24/7</p>
                                <button class="btn btn-outline-primary-custom btn-sm">
                                    <i class="fas fa-phone me-1"></i>اتصل بنا
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // دالة إضافة رصيد
    function addBalance() {
        // محاكاة إضافة رصيد
        showAlert('سيتم إضافة هذه الميزة قريباً', 'info');
    }

    // تحديث الوقت كل ثانية
    function updateTime() {
        const now = new Date();
        const options = { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        
        // يمكن إضافة عنصر لعرض الوقت الحالي
    }

    // تأثيرات الرسوم المتحركة للبطاقات
    document.addEventListener('DOMContentLoaded', function() {
        // تأثير العد للإحصائيات
        const statsNumbers = document.querySelectorAll('.stats-number');
        statsNumbers.forEach(stat => {
            const finalValue = parseInt(stat.textContent);
            let currentValue = 0;
            const increment = finalValue / 50;
            
            const counter = setInterval(() => {
                currentValue += increment;
                if (currentValue >= finalValue) {
                    stat.textContent = finalValue.toLocaleString('ar-YE');
                    clearInterval(counter);
                } else {
                    stat.textContent = Math.floor(currentValue).toLocaleString('ar-YE');
                }
            }, 30);
        });

        // تأثير الهوفر للخدمات
        const serviceCards = document.querySelectorAll('.service-card');
        serviceCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.05)';
                this.style.boxShadow = '0 20px 40px rgba(0,0,0,0.15)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
                this.style.boxShadow = '0 10px 30px rgba(0,0,0,0.1)';
            });
        });
    });

    // تحديث الرصيد في الشريط الجانبي
    function updateSidebarBalance() {
        fetch('/api/balance')
            .then(response => response.json())
            .then(data => {
                document.getElementById('sidebar-balance').textContent = data.balance.toLocaleString('ar-YE');
            })
            .catch(error => console.error('خطأ في تحديث الرصيد:', error));
    }

    // تحديث الرصيد كل دقيقة
    setInterval(updateSidebarBalance, 60000);
</script>
{% endblock %}
