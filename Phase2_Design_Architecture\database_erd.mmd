erDiagram
    %% جداول المستخدمين
    users {
        int id PK
        string username UK
        string email UK
        string phone_number UK
        string password_hash
        string full_name
        enum user_type
        boolean is_active
        boolean is_verified
        timestamp created_at
        timestamp updated_at
    }
    
    user_profiles {
        int id PK
        int user_id FK
        date date_of_birth
        enum gender
        string address
        string city
        string governorate
        string national_id
        string profile_image_url
        timestamp created_at
    }
    
    user_settings {
        int id PK
        int user_id FK
        boolean notifications_enabled
        boolean sms_notifications
        boolean email_notifications
        decimal daily_limit
        decimal monthly_limit
        timestamp created_at
    }
    
    %% جداول المحافظ
    wallets {
        int id PK
        int user_id FK
        decimal balance
        string currency
        boolean is_frozen
        string frozen_reason
        timestamp created_at
        timestamp updated_at
    }
    
    wallet_transactions {
        int id PK
        int wallet_id FK
        enum transaction_type
        decimal amount
        decimal balance_before
        decimal balance_after
        string description
        int related_transaction_id FK
        timestamp created_at
    }
    
    %% جداول مزودي الخدمات
    service_providers {
        int id PK
        string name_ar
        string name_en
        string code UK
        enum provider_type
        string logo_url
        boolean is_active
        json api_config
        timestamp created_at
    }
    
    service_types {
        int id PK
        int provider_id FK
        string name_ar
        string name_en
        string code
        enum category
        decimal min_amount
        decimal max_amount
        decimal fee_percentage
        boolean is_active
    }
    
    service_packages {
        int id PK
        int provider_id FK
        int service_type_id FK
        string name_ar
        string name_en
        string package_code
        decimal price
        string description_ar
        int validity_days
        string data_amount
        int voice_minutes
        int sms_count
        boolean is_active
    }
    
    %% جداول المعاملات
    transactions {
        int id PK
        int user_id FK
        string transaction_id UK
        string api_transaction_id
        int provider_id FK
        int service_type_id FK
        int package_id FK
        string target_number
        decimal amount
        decimal fee
        decimal total_amount
        enum status
        string api_response_code
        text api_response_desc
        timestamp created_at
        timestamp completed_at
    }
    
    transaction_attempts {
        int id PK
        int transaction_id FK
        int attempt_number
        json api_request
        json api_response
        string response_code
        timestamp attempted_at
    }
    
    %% جداول الوكلاء
    agents {
        int id PK
        int user_id FK
        string agent_code UK
        string business_name
        string business_type
        string address
        string city
        string governorate
        decimal latitude
        decimal longitude
        enum status
        decimal daily_limit
        decimal monthly_limit
        timestamp created_at
    }
    
    agent_commissions {
        int id PK
        int agent_id FK
        int transaction_id FK
        decimal commission_rate
        decimal commission_amount
        enum status
        timestamp calculated_at
        timestamp paid_at
    }
    
    %% جداول الأمان والجلسات
    user_sessions {
        int id PK
        int user_id FK
        string session_token UK
        string ip_address
        text user_agent
        boolean is_active
        timestamp expires_at
        timestamp created_at
    }
    
    notifications {
        int id PK
        int user_id FK
        enum type
        string title
        text message
        json data
        boolean is_read
        timestamp created_at
    }
    
    %% العلاقات
    users ||--o{ user_profiles : "has profile"
    users ||--o{ user_settings : "has settings"
    users ||--|| wallets : "owns wallet"
    users ||--o{ transactions : "makes transactions"
    users ||--o{ user_sessions : "has sessions"
    users ||--o{ notifications : "receives notifications"
    users ||--o{ agents : "can be agent"
    
    wallets ||--o{ wallet_transactions : "has transactions"
    
    service_providers ||--o{ service_types : "offers services"
    service_providers ||--o{ service_packages : "has packages"
    service_providers ||--o{ transactions : "processes transactions"
    
    service_types ||--o{ service_packages : "includes packages"
    service_types ||--o{ transactions : "used in transactions"
    
    service_packages ||--o{ transactions : "purchased in transactions"
    
    transactions ||--o{ transaction_attempts : "has attempts"
    transactions ||--o{ wallet_transactions : "affects wallet"
    transactions ||--o{ agent_commissions : "generates commission"
    
    agents ||--o{ agent_commissions : "earns commissions"
