#!/usr/bin/env python3
"""
Yemen Recharge System - Simple Single File Version
تطبيق تعبئة الرصيد اليمني - نسخة مبسطة في ملف واحد
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash, session, jsonify
from werkzeug.security import check_password_hash, generate_password_hash
from datetime import datetime, timedelta
import json

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=24)

# بيانات المستخدمين المؤقتة
users_db = {
    'admin': {
        'id': 1,
        'username': 'admin',
        'password_hash': generate_password_hash('admin123'),
        'full_name': 'مدير النظام',
        'phone': '777123456',
        'user_type': 'admin',
        'balance': 50000
    },
    'customer1': {
        'id': 2,
        'username': 'customer1',
        'password_hash': generate_password_hash('123456'),
        'full_name': 'أحمد محمد علي',
        'phone': '771234567',
        'user_type': 'customer',
        'balance': 15750
    },
    'agent1': {
        'id': 3,
        'username': 'agent1',
        'password_hash': generate_password_hash('agent123'),
        'full_name': 'محل الأمل للاتصالات',
        'phone': '733456789',
        'user_type': 'agent',
        'balance': 25000
    }
}

# بيانات المعاملات المؤقتة
transactions_db = [
    {
        'id': 1,
        'user_id': 2,
        'type': 'recharge',
        'provider': 'يمن موبايل',
        'target_number': '771234567',
        'amount': 1000,
        'status': 'success',
        'created_at': datetime.now() - timedelta(hours=2),
        'description': 'شحن رصيد'
    },
    {
        'id': 2,
        'user_id': 2,
        'type': 'package',
        'provider': 'MTN',
        'target_number': '733456789',
        'amount': 2500,
        'status': 'success',
        'created_at': datetime.now() - timedelta(days=1),
        'description': 'باقة إنترنت 3 جيجا'
    }
]

# قالب HTML الأساسي
BASE_TEMPLATE = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * { font-family: 'Cairo', sans-serif; }
        body { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
            overflow: hidden;
        }
        .navbar-custom {
            background: linear-gradient(135deg, #2E7D32, #4CAF50);
            border: none;
            box-shadow: 0 4px 20px rgba(46, 125, 50, 0.3);
        }
        .btn-primary-custom {
            background: linear-gradient(135deg, #2E7D32, #4CAF50);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(46, 125, 50, 0.3);
        }
        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(46, 125, 50, 0.4);
        }
        .card-custom {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        .form-control-custom {
            border: 2px solid #E0E0E0;
            border-radius: 10px;
            padding: 15px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .form-control-custom:focus {
            border-color: #2E7D32;
            box-shadow: 0 0 0 0.2rem rgba(46, 125, 50, 0.25);
        }
        .alert-custom {
            border: none;
            border-radius: 10px;
            padding: 15px 20px;
            margin-bottom: 20px;
        }
        .stats-card {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            border: 1px solid #E0E0E0;
            transition: all 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    {% if show_nav %}
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="{{ url_for('dashboard') }}">
                <i class="fas fa-mobile-alt me-2"></i>
                منصة تعبئة الرصيد
            </a>
            <div class="navbar-nav me-auto">
                <a class="nav-link text-white" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <a class="nav-link text-white" href="{{ url_for('recharge') }}">
                    <i class="fas fa-mobile-alt me-1"></i>شحن رصيد
                </a>
            </div>
            <div class="navbar-nav">
                <span class="nav-link text-white">
                    مرحباً، {{ session.get('user_name', 'زائر') }}
                </span>
                <a class="nav-link text-white" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>خروج
                </a>
            </div>
        </div>
    </nav>
    {% endif %}
    
    <div class="container-fluid">
        <div class="main-container">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'info' }} alert-custom alert-dismissible fade show m-4" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            {{ content|safe }}
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
"""

def render_page(content, title="منصة تعبئة الرصيد اليمنية", show_nav=False):
    """دالة لعرض الصفحات"""
    return render_template_string(BASE_TEMPLATE, 
                                content=content, 
                                title=title, 
                                show_nav=show_nav)

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if not username or not password:
            flash('يرجى إدخال اسم المستخدم وكلمة المرور', 'error')
        else:
            user = users_db.get(username)
            if user and check_password_hash(user['password_hash'], password):
                session['user_id'] = user['id']
                session['username'] = user['username']
                session['user_name'] = user['full_name']
                session['user_type'] = user['user_type']
                session['balance'] = user['balance']
                flash(f'مرحباً بك {user["full_name"]}!', 'success')
                return redirect(url_for('dashboard'))
            else:
                flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    content = """
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card-custom p-4 mt-5">
                <div class="text-center mb-4">
                    <i class="fas fa-mobile-alt" style="font-size: 3rem; color: #2E7D32;"></i>
                    <h2 class="fw-bold mt-3">تسجيل الدخول</h2>
                </div>
                
                <form method="POST">
                    <div class="mb-3">
                        <label class="form-label fw-semibold">اسم المستخدم</label>
                        <input type="text" class="form-control form-control-custom" name="username" required>
                    </div>
                    
                    <div class="mb-4">
                        <label class="form-label fw-semibold">كلمة المرور</label>
                        <input type="password" class="form-control form-control-custom" name="password" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary-custom w-100 mb-3">
                        تسجيل الدخول
                    </button>
                </form>
                
                <div class="mt-4 p-3 bg-light rounded">
                    <h6 class="fw-bold mb-3 text-center">حسابات تجريبية:</h6>
                    <div class="row g-2">
                        <div class="col-12">
                            <button class="btn btn-outline-primary btn-sm w-100" onclick="fillDemo('admin', 'admin123')">
                                👨‍💼 مدير النظام
                            </button>
                        </div>
                        <div class="col-12">
                            <button class="btn btn-outline-primary btn-sm w-100" onclick="fillDemo('customer1', '123456')">
                                👤 عميل
                            </button>
                        </div>
                        <div class="col-12">
                            <button class="btn btn-outline-primary btn-sm w-100" onclick="fillDemo('agent1', 'agent123')">
                                🏪 وكيل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function fillDemo(username, password) {
            document.querySelector('input[name="username"]').value = username;
            document.querySelector('input[name="password"]').value = password;
        }
    </script>
    """
    
    return render_page(content, "تسجيل الدخول")

@app.route('/logout')
def logout():
    """تسجيل الخروج"""
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
def dashboard():
    """لوحة التحكم الرئيسية"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    user_id = session['user_id']
    user_transactions = [t for t in transactions_db if t['user_id'] == user_id]
    
    stats = {
        'total_transactions': len(user_transactions),
        'successful_transactions': len([t for t in user_transactions if t['status'] == 'success']),
        'total_spent': sum([t['amount'] for t in user_transactions if t['status'] == 'success'])
    }
    
    content = f"""
    <div class="p-4">
        <div class="row mb-4">
            <div class="col-12">
                <h2 class="fw-bold text-dark mb-1">مرحباً، {session['user_name']}</h2>
                <p class="text-muted">
                    <i class="fas fa-calendar me-1"></i>
                    {datetime.now().strftime('%A، %d %B %Y')}
                </p>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="text-primary mb-2">
                        <i class="fas fa-wallet" style="font-size: 2rem;"></i>
                    </div>
                    <div class="h4 fw-bold">{session['balance']:,} ريال</div>
                    <div class="text-muted">رصيدك الحالي</div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="text-success mb-2">
                        <i class="fas fa-chart-line" style="font-size: 2rem;"></i>
                    </div>
                    <div class="h4 fw-bold">{stats['total_transactions']}</div>
                    <div class="text-muted">إجمالي المعاملات</div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="text-info mb-2">
                        <i class="fas fa-check-circle" style="font-size: 2rem;"></i>
                    </div>
                    <div class="h4 fw-bold">{stats['successful_transactions']}</div>
                    <div class="text-muted">معاملات ناجحة</div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="text-warning mb-2">
                        <i class="fas fa-money-bill-wave" style="font-size: 2rem;"></i>
                    </div>
                    <div class="h4 fw-bold">{stats['total_spent']:,} ريال</div>
                    <div class="text-muted">إجمالي الإنفاق</div>
                </div>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-12">
                <div class="card-custom">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-rocket me-2"></i>
                            الخدمات السريعة
                        </h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <a href="{url_for('recharge')}" class="text-decoration-none">
                                    <div class="text-center p-4 border rounded">
                                        <i class="fas fa-mobile-alt text-primary mb-3" style="font-size: 2.5rem;"></i>
                                        <h6 class="fw-bold">شحن رصيد</h6>
                                        <p class="text-muted small mb-0">شحن رصيد الهاتف فوراً</p>
                                    </div>
                                </a>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-4 border rounded">
                                    <i class="fas fa-box text-success mb-3" style="font-size: 2.5rem;"></i>
                                    <h6 class="fw-bold">الباقات</h6>
                                    <p class="text-muted small mb-0">باقات الإنترنت والمكالمات</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-4 border rounded">
                                    <i class="fas fa-file-invoice text-warning mb-3" style="font-size: 2.5rem;"></i>
                                    <h6 class="fw-bold">دفع الفواتير</h6>
                                    <p class="text-muted small mb-0">فواتير الكهرباء والماء</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-4 border rounded">
                                    <i class="fas fa-exchange-alt text-info mb-3" style="font-size: 2.5rem;"></i>
                                    <h6 class="fw-bold">تحويل أموال</h6>
                                    <p class="text-muted small mb-0">تحويل للأصدقاء والعائلة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <div class="card-custom">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2"></i>
                            آخر المعاملات
                        </h5>
                    </div>
                    <div class="card-body">
                        {"".join([f'''
                        <div class="d-flex justify-content-between align-items-center border-bottom py-3">
                            <div>
                                <h6 class="mb-1">{t["description"]}</h6>
                                <small class="text-muted">{t["provider"]} - {t["target_number"]}</small>
                            </div>
                            <div class="text-end">
                                <div class="fw-bold">{t["amount"]:,} ريال</div>
                                <span class="badge bg-{'success' if t['status'] == 'success' else 'warning'}">
                                    {'نجح' if t['status'] == 'success' else 'معالجة'}
                                </span>
                            </div>
                        </div>
                        ''' for t in user_transactions[:5]]) if user_transactions else '<div class="text-center p-4"><p class="text-muted">لا توجد معاملات حتى الآن</p></div>'}
                    </div>
                </div>
            </div>
        </div>
    </div>
    """
    
    return render_page(content, "لوحة التحكم", show_nav=True)

@app.route('/recharge', methods=['GET', 'POST'])
def recharge():
    """صفحة شحن الرصيد"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    if request.method == 'POST':
        provider = request.form.get('provider')
        phone_number = request.form.get('phone_number')
        amount = int(request.form.get('amount', 0))
        
        if not all([provider, phone_number, amount]):
            flash('يرجى إكمال جميع البيانات المطلوبة', 'error')
        elif amount < 100 or amount > 50000:
            flash('المبلغ يجب أن يكون بين 100 و 50,000 ريال', 'error')
        elif amount > session['balance']:
            flash('رصيدك غير كافي لإتمام هذه العملية', 'error')
        else:
            # محاكاة معالجة الشحن
            new_transaction = {
                'id': len(transactions_db) + 1,
                'user_id': session['user_id'],
                'type': 'recharge',
                'provider': provider,
                'target_number': phone_number,
                'amount': amount,
                'status': 'success',
                'created_at': datetime.now(),
                'description': 'شحن رصيد'
            }
            
            transactions_db.append(new_transaction)
            
            # تحديث الرصيد
            session['balance'] -= amount
            users_db[session['username']]['balance'] = session['balance']
            
            flash(f'تم شحن رصيد الرقم {phone_number} بمبلغ {amount:,} ريال بنجاح!', 'success')
            return redirect(url_for('dashboard'))
    
    content = """
    <div class="p-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="text-center mb-4">
                    <i class="fas fa-mobile-alt" style="font-size: 4rem; color: #2E7D32;"></i>
                    <h2 class="fw-bold mt-3">شحن رصيد الهاتف</h2>
                    <p class="text-muted">اختر الشركة وأدخل الرقم والمبلغ لشحن الرصيد فوراً</p>
                </div>
                
                <div class="card-custom">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">بيانات الشحن</h5>
                    </div>
                    <div class="card-body p-4">
                        <form method="POST">
                            <div class="mb-4">
                                <label class="form-label fw-semibold">شركة الاتصالات</label>
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <input type="radio" class="btn-check" name="provider" id="yemen_mobile" value="يمن موبايل" checked>
                                        <label class="btn btn-outline-primary w-100 p-3" for="yemen_mobile">
                                            <i class="fas fa-mobile-alt d-block mb-2"></i>
                                            يمن موبايل
                                        </label>
                                    </div>
                                    <div class="col-md-3">
                                        <input type="radio" class="btn-check" name="provider" id="mtn" value="MTN">
                                        <label class="btn btn-outline-primary w-100 p-3" for="mtn">
                                            <i class="fas fa-signal d-block mb-2"></i>
                                            MTN
                                        </label>
                                    </div>
                                    <div class="col-md-3">
                                        <input type="radio" class="btn-check" name="provider" id="sabaphone" value="سبأفون">
                                        <label class="btn btn-outline-primary w-100 p-3" for="sabaphone">
                                            <i class="fas fa-phone d-block mb-2"></i>
                                            سبأفون
                                        </label>
                                    </div>
                                    <div class="col-md-3">
                                        <input type="radio" class="btn-check" name="provider" id="y" value="واي">
                                        <label class="btn btn-outline-primary w-100 p-3" for="y">
                                            <i class="fas fa-wifi d-block mb-2"></i>
                                            واي
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label class="form-label fw-semibold">رقم الهاتف</label>
                                <div class="input-group">
                                    <span class="input-group-text">+967</span>
                                    <input type="tel" class="form-control form-control-custom" name="phone_number" 
                                           placeholder="7XXXXXXXX" pattern="[7][0-9]{8}" maxlength="9" required>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label class="form-label fw-semibold">مبلغ الشحن</label>
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <input type="radio" class="btn-check" name="amount" id="amount_500" value="500">
                                        <label class="btn btn-outline-success w-100 p-3" for="amount_500">
                                            <div class="fw-bold">500</div>
                                            <small>ريال</small>
                                        </label>
                                    </div>
                                    <div class="col-md-3">
                                        <input type="radio" class="btn-check" name="amount" id="amount_1000" value="1000" checked>
                                        <label class="btn btn-outline-success w-100 p-3" for="amount_1000">
                                            <div class="fw-bold">1,000</div>
                                            <small>ريال</small>
                                        </label>
                                    </div>
                                    <div class="col-md-3">
                                        <input type="radio" class="btn-check" name="amount" id="amount_2000" value="2000">
                                        <label class="btn btn-outline-success w-100 p-3" for="amount_2000">
                                            <div class="fw-bold">2,000</div>
                                            <small>ريال</small>
                                        </label>
                                    </div>
                                    <div class="col-md-3">
                                        <input type="radio" class="btn-check" name="amount" id="amount_5000" value="5000">
                                        <label class="btn btn-outline-success w-100 p-3" for="amount_5000">
                                            <div class="fw-bold">5,000</div>
                                            <small>ريال</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary-custom btn-lg">
                                    <i class="fas fa-credit-card me-2"></i>
                                    تأكيد الشحن
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    """
    
    return render_page(content, "شحن رصيد", show_nav=True)

if __name__ == '__main__':
    print("🚀 بدء تشغيل منصة تعبئة الرصيد اليمنية...")
    print("📱 الخدمات المتاحة:")
    print("   - شحن رصيد الهاتف")
    print("   - إدارة المحفظة")
    print("   - تقارير المعاملات")
    print("\n🔐 حسابات تجريبية:")
    print("   مدير: admin / admin123")
    print("   عميل: customer1 / 123456")
    print("   وكيل: agent1 / agent123")
    print("\n🌐 الرابط: http://localhost:5000")
    print("=" * 50)
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        use_reloader=True,
        threaded=True
    )
